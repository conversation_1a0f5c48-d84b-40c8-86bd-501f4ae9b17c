import json

from constants.extracted_data import ConversationState
from core.schemas import CustomModel


__all__ = ['ConfirmedData']


class ConfirmedData(CustomModel):
    """Schema for user-confirmed qual data."""

    # Core fields (existing)
    client_name: str | None = None
    ldmf_country: str | None = None
    date_intervals: tuple[str | None, str | None] | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None
    proposed_client_name: str | None = None  # For tracking client names during creation flow
    last_confirmed_field: str | None = None  # Track what field was most recently confirmed

    # Engagement Description fields
    business_issues: str | None = None
    scope_approach: str | None = None
    value_delivered: str | None = None
    engagement_summary: str | None = None
    one_line_description: str | None = None

    # Engagement Details fields
    client_references: str | None = None
    client_name_sharing: str | None = None
    client_industry: str | None = None
    engagement_dates: str | None = None
    engagement_locations: str | None = None
    engagement_fee_display: str | None = None
    client_services: str | None = None
    source_of_work: str | None = None

    # Usage & Team fields
    qual_usage: str | None = None
    team_roles: str | None = None
    approver: str | None = None

    @classmethod
    def from_json_string(cls, json_str: str | None) -> 'ConfirmedData':
        """Create ConfirmedData from JSON string stored in database."""
        if not json_str:
            return cls()
        try:
            data = json.loads(json_str)
            return cls.model_validate(data)
        except (json.JSONDecodeError, ValueError):
            return cls()

    def to_json_string(self) -> str:
        """Convert ConfirmedData to JSON string for database storage."""
        return json.dumps(self.model_dump(exclude_none=True), default=str)

    @property
    def required_fields_are_complete(self) -> bool:
        """Check if core required fields are complete (maintains backward compatibility)."""
        start_date, end_date = self.date_intervals or (None, None)
        return all(
            [
                self.client_name is not None and len(self.client_name.strip()) > 0,
                self.ldmf_country is not None and len(self.ldmf_country.strip()) > 0,
                start_date is not None,
                end_date is not None,
                self.objective_and_scope is not None and self.objective_and_scope.strip() != '',
                self.outcomes is not None and self.outcomes.strip() != '',
            ]
        )

    @property
    def comprehensive_fields_are_complete(self) -> bool:
        """Check if all comprehensive fields (including new ones) are complete."""
        core_complete = self.required_fields_are_complete
        if not core_complete:
            return False

        # Check additional comprehensive fields
        additional_fields_complete = all(
            [
                # Engagement Description fields
                self.business_issues is not None and self.business_issues.strip() != '',
                self.scope_approach is not None and self.scope_approach.strip() != '',
                self.value_delivered is not None and self.value_delivered.strip() != '',
                self.engagement_summary is not None and self.engagement_summary.strip() != '',
                self.one_line_description is not None and self.one_line_description.strip() != '',
                # Engagement Details fields
                self.client_references is not None and self.client_references.strip() != '',
                self.client_name_sharing is not None and self.client_name_sharing.strip() != '',
                self.client_industry is not None and self.client_industry.strip() != '',
                self.engagement_dates is not None and self.engagement_dates.strip() != '',
                self.engagement_locations is not None and self.engagement_locations.strip() != '',
                self.engagement_fee_display is not None and self.engagement_fee_display.strip() != '',
                self.client_services is not None and self.client_services.strip() != '',
                self.source_of_work is not None and self.source_of_work.strip() != '',
                # Usage & Team fields
                self.qual_usage is not None and self.qual_usage.strip() != '',
                self.team_roles is not None and self.team_roles.strip() != '',
                self.approver is not None and self.approver.strip() != '',
            ]
        )

        return additional_fields_complete

    @property
    def is_empty(self) -> bool:
        return all(
            [
                self.client_name is None,
                self.ldmf_country is None,
                self.date_intervals is None,
                self.objective_and_scope is None,
                self.outcomes is None,
            ]
        )

    def get_current_conversation_state(self) -> ConversationState:
        """
        Determines the current conversation state based on the completeness of confirmed data fields.
        """
        if not self.client_name:
            return ConversationState.COLLECTING_CLIENT_NAME
        if not self.ldmf_country:
            return ConversationState.COLLECTING_COUNTRY
        if not self.date_intervals:
            return ConversationState.COLLECTING_DATES
        if not self.objective_and_scope:
            return ConversationState.COLLECTING_OBJECTIVE
        if not self.outcomes:
            return ConversationState.COLLECTING_OUTCOMES
        return ConversationState.DATA_COMPLETE
