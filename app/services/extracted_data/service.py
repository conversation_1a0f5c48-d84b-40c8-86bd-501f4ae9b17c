import asyncio
import logging
from typing import Any, Sequence
from uuid import UUID

from constants.extracted_data import (
    ConfirmedDataFields,
    ConversationState,
    DataSourceType,
    FieldStatus,
    MissingDataStatus,
    RequiredField,
)
from constants.message import SystemReplyType
from repositories import (
    ConversationRepository,
    ExtractedDataRepository,
    QualsClientsRepository,
)
from schemas import AggregatedData, ConfirmedData, ExtractedData, MissingDataResponse
from schemas.conversation_message.option import LDMFCountryOption
from schemas.extracted_data import FieldHandlerResponse
from services.client_industry import IndustryDataService
from services.ldmf_country import LDMFCountryService
from services.project_role import RoleDataService
from services.project_service import ServiceDataService

from .handlers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ClientNameH<PERSON><PERSON>,
    DateIntervalsHandler,
    LDMFCountryHandler,
    ObjectiveHandler,
    Outcomes<PERSON><PERSON><PERSON>,
    TokenRequiredFieldHandler,
)
from .parsers import KXDashDataParser, PromptAndDocumentDataParser
from .strategies import (
    ConcatenationStrategy,
    DateIntervalCollectionStrategy,
    EnhancedDateIntervalCollectionStrategy,
    EnhancedMergeUniqueValuesStrategy,
    EnhancedPriorityOverrideStrategy,
    MergeUniqueValuesStrategy,
    PriorityOverrideStrategy,
)


__all__ = ['ExtractedDataService']


logger = logging.getLogger(__name__)


class ExtractedDataService:
    _SOURCE_TYPE_TO_PARSER_MAP = {
        DataSourceType.KX_DASH: KXDashDataParser,
        DataSourceType.DOCUMENTS: PromptAndDocumentDataParser,
        DataSourceType.PROMPT: PromptAndDocumentDataParser,
    }

    _SOURCE_DATA_PROCESSING_PRIORITY = [DataSourceType.KX_DASH, DataSourceType.DOCUMENTS, DataSourceType.PROMPT]

    _FIELD_HANDLERS: dict[RequiredField, BaseFieldHandler | TokenRequiredFieldHandler] = {
        RequiredField.ENGAGEMENT_DATES: DateIntervalsHandler(),
        RequiredField.OBJECTIVE_SCOPE: ObjectiveHandler(),
        RequiredField.OUTCOMES: OutcomesHandler(),
    }

    # Define the order in which fields should be collected
    _FIELD_COLLECTION_ORDER = [
        RequiredField.CLIENT_INFO,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    ]

    # Map fields to conversation states
    _FIELD_TO_STATE_MAP = {
        RequiredField.CLIENT_INFO: ConversationState.COLLECTING_CLIENT_NAME,
        RequiredField.LDMF_COUNTRY: ConversationState.COLLECTING_COUNTRY,
        RequiredField.ENGAGEMENT_DATES: ConversationState.COLLECTING_DATES,
        RequiredField.OBJECTIVE_SCOPE: ConversationState.COLLECTING_OBJECTIVE,
        RequiredField.OUTCOMES: ConversationState.COLLECTING_OUTCOMES,
    }

    def __init__(
        self,
        industry_data_service: IndustryDataService,
        role_data_service: RoleDataService,
        service_data_service: ServiceDataService,
        extracted_data_repository: ExtractedDataRepository,
        conversation_repository: ConversationRepository,
        quals_clients_repository: QualsClientsRepository,
        ldmf_country_service: LDMFCountryService,
    ):
        self.extracted_data_repository = extracted_data_repository
        self.industry_data_service = industry_data_service
        self.role_data_service = role_data_service
        self.service_data_service = service_data_service
        self.conversation_repository = conversation_repository
        self.quals_clients_repository = quals_clients_repository
        self.ldmf_country_service = ldmf_country_service
        self._FIELD_HANDLERS[RequiredField.CLIENT_INFO] = ClientNameHandler(quals_clients_repository)
        self._FIELD_HANDLERS[RequiredField.LDMF_COUNTRY] = LDMFCountryHandler(ldmf_country_service)

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data from.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete_many(conversation_id)
        except Exception as e:  # pragma: no cover
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def _process_activity_data(self, activity_data: dict[str, Any], token: str) -> dict[str, Any]:
        """
        Process the activity data.
        """
        # Initialize default empty dictionaries for external data
        industries: dict[str, dict[str, Any]] = {}
        services: dict[str, dict[str, Any]] = {}
        roles: dict[str, dict[str, Any]] = {}

        try:
            # Attempt to fetch external data with proper error handling
            results = await asyncio.gather(
                self.industry_data_service.list(token),
                self.service_data_service.list(token),
                self.role_data_service.list(token),
                return_exceptions=True,
            )

            # Handle cases where gather returns exceptions instead of data
            industries_result, services_result, roles_result = results

            if isinstance(industries_result, Exception):
                logger.warning('Failed to fetch industries data: %s', industries_result)
                industries = {}
            else:
                industries = industries_result  # type: ignore[assignment]

            if isinstance(services_result, Exception):
                logger.warning('Failed to fetch services data: %s', services_result)
                services = {}
            else:
                services = services_result  # type: ignore[assignment]

            if isinstance(roles_result, Exception):
                logger.warning('Failed to fetch roles data: %s', roles_result)
                roles = {}
            else:
                roles = roles_result  # type: ignore[assignment]

        except Exception as e:
            logger.warning('Failed to fetch external reference data: %s', e)
            # Continue processing with empty dictionaries

        # Process industry data if available
        industry_name = activity_data.get('global_industry')
        if industry_name and industries:
            industry = industries.get(industry_name)
            if industry:
                activity_data['industries'] = [industry['id']]

        # Process service data if available
        service_name = activity_data.get('global_service')
        if service_name and services:
            service = services.get(service_name)
            if service:
                activity_data['services'] = [service['id']]

        # Process role data if available
        lep_emails = activity_data.get('engagement_lep_emails')
        lcsp_emails = activity_data.get('engagement_lcsp_emails')
        manager_emails = activity_data.get('engagement_manager_emails')

        processed_roles = []
        if roles:  # Only process roles if we have role data
            if lep_emails:
                lep_role_id = roles.get('Lead Engagement Partner', {}).get('id')
                if lep_role_id:
                    processed_roles.extend({'email': email, 'roles': [lep_role_id]} for email in lep_emails)
            if lcsp_emails:
                lcsp_role_id = roles.get('LCSP', {}).get('id')
                if lcsp_role_id:
                    processed_roles.extend({'email': email, 'roles': [lcsp_role_id]} for email in lcsp_emails)
            if manager_emails:
                manager_role_id = roles.get('Engagement Manager', {}).get('id')
                if manager_role_id:
                    processed_roles.extend({'email': email, 'roles': [manager_role_id]} for email in manager_emails)

        activity_data['roles'] = processed_roles

        return activity_data

    async def update(
        self, conversation_id: UUID, raw_data: dict[str, Any], source_type: DataSourceType, token: str
    ) -> ExtractedData:
        """
        Update extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to update extracted data for.
            raw_data: The data to update the extracted data with.
            source_type: The type of data source being updated.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
            ServiceExceptionError: If the extracted data record is not found.
        """
        logger.debug('Started updating "%s" extracted data for conversation ID "%s"', source_type, conversation_id)
        try:
            extracted_data = await self.extracted_data_repository.get(
                conversation_id=conversation_id,
                data_source_type=source_type,
            ) or ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
            processed_activity_data = await self._process_activity_data(raw_data, token)
            parser = self._SOURCE_TYPE_TO_PARSER_MAP[source_type]()
            await self.extracted_data_repository.update(parser(extracted_data, processed_activity_data))
            return extracted_data

        except Exception as e:  # pragma: no cover
            logger.error(
                'Failed to update "%s" extracted data for conversation ID "%s": %s', source_type, conversation_id, e
            )
            raise

    async def aggregate_data(self, conversation_id: UUID) -> AggregatedData:
        """
        Aggregate data from all sources for a conversation, applying different strategies:
        - client_name, ldmf_country, title: Merge unique values from all sources
        - date_intervals: Collect date ranges as (start_date, end_date) pairs from all sources
        - objective_and_scope, outcomes: Priority-based override (highest priority wins)

        Args:
            conversation_id: The ID of the conversation to aggregate data for.

        Returns:
            AggregatedData: The aggregated data from all sources.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        # Initialize aggregation strategies
        merge_strategy = MergeUniqueValuesStrategy()
        date_strategy = DateIntervalCollectionStrategy()
        priority_strategy = PriorityOverrideStrategy()

        # Initialize enhanced strategies for new fields
        concatenation_strategy = ConcatenationStrategy()
        enhanced_merge_strategy = EnhancedMergeUniqueValuesStrategy()
        enhanced_priority_strategy = EnhancedPriorityOverrideStrategy()
        enhanced_date_strategy = EnhancedDateIntervalCollectionStrategy()

        # Process data sources in priority order (lowest to highest)
        for source_type in self._SOURCE_DATA_PROCESSING_PRIORITY:
            try:
                extracted_data = await self.extracted_data_repository.get(
                    conversation_id=conversation_id,
                    data_source_type=source_type,
                )
                if not extracted_data:
                    logger.debug('No %s data found for conversation %s', source_type, conversation_id)
                    continue

                # Apply aggregation strategies
                merge_strategy.process(extracted_data)
                date_strategy.process(extracted_data)
                priority_strategy.process(extracted_data)

                # Apply enhanced strategies for new fields
                concatenation_strategy.process(extracted_data)
                enhanced_merge_strategy.process(extracted_data)
                enhanced_priority_strategy.process(extracted_data)
                enhanced_date_strategy.process(extracted_data)

            except Exception as e:
                logger.warning('Error processing %s data for conversation %s: %s', source_type, conversation_id, e)
                continue

        return AggregatedData(
            # Core fields (existing)
            client_name=merge_strategy.get_client_names(),
            ldmf_country=merge_strategy.get_ldmf_countries(),
            date_intervals=date_strategy.get_date_intervals(),
            date_intervals_original=date_strategy.get_date_intervals_original(),
            objective_and_scope=priority_strategy.get_objective_and_scope(),
            outcomes=priority_strategy.get_outcomes(),
            # Engagement Description fields
            business_issues=concatenation_strategy.get_business_issues(),
            scope_approach=concatenation_strategy.get_scope_approach(),
            value_delivered=concatenation_strategy.get_value_delivered(),
            engagement_summary=concatenation_strategy.get_engagement_summary(),
            one_line_description=concatenation_strategy.get_one_line_description(),
            # Engagement Details fields
            client_references=concatenation_strategy.get_client_references(),
            client_name_sharing=concatenation_strategy.get_client_name_sharing(),
            client_industry=enhanced_merge_strategy.get_client_industry(),
            engagement_dates=enhanced_date_strategy.get_engagement_dates(),
            engagement_locations=enhanced_merge_strategy.get_engagement_locations(),
            engagement_fee_display=enhanced_priority_strategy.get_engagement_fee_display(),
            client_services=enhanced_merge_strategy.get_client_services(),
            source_of_work=concatenation_strategy.get_source_of_work(),
            # Usage & Team fields
            qual_usage=concatenation_strategy.get_qual_usage(),
            team_roles=concatenation_strategy.get_team_roles(),
            approver=concatenation_strategy.get_approver(),
        )

    async def get_missing_required_data_prompts(
        self,
        conversation_id: UUID,
        token: str,
        confirmed_data: ConfirmedData | None = None,
    ) -> MissingDataResponse:
        """
        Method for progressive data collection with user confirmation tracking.

        Args:
            conversation_id: The ID of the conversation to check.
            confirmed_data: User-confirmed data from previous interactions.

        Returns:
            MissingDataResponse: Structured response with next steps for data collection.
        """
        logger.debug('Enhanced missing data check for conversation ID: %s', conversation_id)

        if confirmed_data is None:
            confirmed_data = ConfirmedData()

        try:
            aggregated_data = await self.aggregate_data(conversation_id)
            missing_fields = []

            # Find the first field that needs confirmation or auto-confirmation
            for field_type in self._FIELD_COLLECTION_ORDER:
                handler = self._FIELD_HANDLERS[field_type]
                response = await self._call_handler_check_and_get_response(
                    handler, aggregated_data, confirmed_data, token
                )

                if response.needs_confirmation:
                    missing_fields.append(response.next_expected_field or field_type.value)

                # Handle auto-confirmation cases for fields with confident matches
                if (
                    not response.needs_confirmation
                    and response.field_status == FieldStatus.CONFIRMED
                    and response.options
                ):
                    # Define field-specific auto-confirmation mapping
                    auto_confirm_mapping = {
                        RequiredField.CLIENT_INFO: (
                            ConfirmedDataFields.CLIENT_NAME.value,
                            ConversationState.COLLECTING_COUNTRY,
                        ),
                        RequiredField.LDMF_COUNTRY: (
                            ConfirmedDataFields.LDMF_COUNTRY.value,
                            ConversationState.COLLECTING_DATES,
                        ),
                        RequiredField.ENGAGEMENT_DATES: (
                            ConfirmedDataFields.DATE_INTERVALS.value,
                            ConversationState.COLLECTING_OBJECTIVE,
                        ),
                    }

                    if field_type in auto_confirm_mapping:
                        field_name, next_state = auto_confirm_mapping[field_type]
                        auto_confirmed_value = response.options[0]

                        try:
                            await self.update_confirmed_data(
                                conversation_id=conversation_id,
                                field_name=field_name,
                                field_value=auto_confirmed_value,
                                state=next_state,
                            )
                        except Exception as e:
                            logger.error('Failed to auto-confirm %s: %s', field_name, e)
                            # Fall through to normal confirmation flow
                            pass
                        else:
                            continue

                if response.needs_confirmation:
                    if response.system_reply_type == SystemReplyType.EXTRACTED_LDMF_NOT_VALID:
                        return MissingDataResponse(
                            status=MissingDataStatus.EXTRACTED_DATA_NOT_VALID,
                            message=response.system_message,
                            reply_type=response.system_reply_type,
                            next_expected_field=response.next_expected_field,
                            missing_fields=missing_fields,
                            conversation_state=self._FIELD_TO_STATE_MAP[field_type],
                            options=response.options,
                        )
                    if response.system_reply_type == SystemReplyType.CLIENT_NOT_FOUND:
                        return MissingDataResponse(
                            status=MissingDataStatus.CLIENT_NAME_NOT_IN_API,
                            message=response.system_message,
                            reply_type=response.system_reply_type,
                            next_expected_field=response.next_expected_field,
                            missing_fields=missing_fields,
                            conversation_state=self._FIELD_TO_STATE_MAP[field_type],
                            options=[],
                        )
                    return MissingDataResponse(
                        status=MissingDataStatus.MISSING_DATA,
                        message=response.system_message,
                        reply_type=response.system_reply_type,
                        next_expected_field=response.next_expected_field,
                        missing_fields=missing_fields,
                        conversation_state=self._FIELD_TO_STATE_MAP[field_type],
                        options=response.options,
                    )

            # All fields are confirmed
            return MissingDataResponse(
                status=MissingDataStatus.DATA_COMPLETE,
                message=None,
                reply_type=None,
                next_expected_field=None,
                missing_fields=[],
                conversation_state=ConversationState.DATA_COMPLETE,
            )

        except Exception as e:
            logger.error("Failed to check enhanced missing data for conversation ID '%s': %s", conversation_id, e)
            return MissingDataResponse(
                status=MissingDataStatus.ERROR,
                message='An error occurred while checking data completeness.',
                reply_type=None,
                next_expected_field=None,
                missing_fields=[],
                conversation_state=ConversationState.INITIAL,
            )

    async def delete(self, conversation_id: UUID, data_source_type: DataSourceType) -> None:
        """
        Delete extracted data of a specified source type for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data for.
            data_source_type: The type of data source to delete extracted data for.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete(conversation_id, data_source_type)

        except Exception as e:
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def update_confirmed_data(
        self,
        conversation_id: UUID,
        field_name: str,
        field_value: str | Sequence[str | None],
        state: ConversationState,
    ) -> None:
        """
        Update confirmed data for a specific field and update conversation state.

        Args:
            conversation_id: The ID of the conversation to update
            field_name: The name of the field to update (e.g., 'client_name')
            field_value: The confirmed value for the field
            state: The new conversation state

        Raises:
            EntityNotFoundError: If the conversation does not exist
        """
        logger.debug('Updating confirmed data for conversation ID: %s, field: %s', conversation_id, field_name)

        try:
            # Get current confirmed data
            current_confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

            # Update the specific field
            updated_data = current_confirmed_data.model_copy()
            setattr(updated_data, field_name, field_value)
            # Track which field was just confirmed
            updated_data.last_confirmed_field = field_name

            # Determine the next conversation state based on the updated confirmed data
            next_state = updated_data.get_current_conversation_state()

            # Update both confirmed data and state
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=conversation_id, confirmed_data=updated_data, state=next_state
            )

            logger.debug('Successfully updated confirmed data for conversation ID: %s', conversation_id)

        except Exception as e:
            logger.error('Failed to update confirmed data for conversation ID: %s: %s', conversation_id, e)
            raise

    async def _call_handler_check_and_get_response(
        self,
        handler: BaseFieldHandler | TokenRequiredFieldHandler,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
    ) -> FieldHandlerResponse:
        # Call the appropriate method based on handler type
        if isinstance(handler, TokenRequiredFieldHandler):
            response = await handler.check_and_get_response(aggregated_data, confirmed_data, token)
        else:
            response = await handler.check_and_get_response(aggregated_data, confirmed_data)

        return response

    async def _call_handler_to_validate_value(
        self,
        handler: BaseFieldHandler | TokenRequiredFieldHandler,
        token: str,
        field_value: Any,
    ) -> FieldHandlerResponse:
        if isinstance(handler, TokenRequiredFieldHandler):
            response = await handler.validate_value(token, field_value)
        else:
            response = await handler.validate_value(field_value)

        return response

    def catch_invalid_ldmf_option(
        self,
        missing_data_response: MissingDataResponse,
        conversation_state: ConversationState,
        system_reply_type: SystemReplyType | None,
    ) -> LDMFCountryOption | None:
        """
        Catch invalid LDMF country option and return a LDMFCountryOption with an empty string.

        Args:
            missing_data_response: The missing data response
            conversation_state: The current conversation state

        Returns:
            LDMFCountryOption: The LDMFCountryOption with an empty string if the conversation state is COLLECTING_COUNTRY and the missing data response status is EXTRACTED_DATA_NOT_VALID, None otherwise.
        """
        if (
            missing_data_response.status
            in [
                MissingDataStatus.EXTRACTED_DATA_NOT_VALID,
                MissingDataStatus.USER_INSERTS_LDMF,
                MissingDataStatus.MISSING_DATA,
            ]
            and conversation_state == ConversationState.COLLECTING_COUNTRY
        ):
            return LDMFCountryOption(ldmf_country='')
        if system_reply_type and system_reply_type == SystemReplyType.EXTRACTED_LDMF_NOT_VALID:
            return LDMFCountryOption(ldmf_country='')
        return None

    async def single_value_validation(
        self,
        field: RequiredField,
        field_value: Any,
        token: str,
    ) -> FieldHandlerResponse:
        """
        Validate a single value for a field.

        Args:
            conversation_id: The conversation ID
            field_name: The name of the field to validate
            field_value: The value to validate
            token: The user's token

        Returns:
            True if the value is valid, False otherwise
        """
        try:
            handler = self._FIELD_HANDLERS[field]
            response = await self._call_handler_to_validate_value(handler, token, field_value)
            return response
        except Exception:
            logger.exception('Failed to validate %s', field)
            raise
