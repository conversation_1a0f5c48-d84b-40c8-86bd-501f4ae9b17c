from schemas import ExtractedData

from .base import BaseAggregationStrategy


__all__ = ['DateIntervalCollectionStrategy']


class DateIntervalCollectionStrategy(BaseAggregationStrategy):
    """Strategy for collecting date intervals as pairs from all sources."""

    def __init__(self):
        self._date_intervals: set[tuple[str | None, str | None]] = set()
        self._date_intervals_original: set[tuple[str | None, str | None]] = set()

    def process(self, extracted_data: ExtractedData) -> None:
        """Collect date intervals from extracted data."""
        self._process_parsed_dates(extracted_data)
        self._process_original_dates(extracted_data)

    def _process_parsed_dates(self, extracted_data: ExtractedData) -> None:
        """Process parsed date fields."""
        if extracted_data.start_date or extracted_data.end_date:
            start_date_str = extracted_data.start_date.isoformat() if extracted_data.start_date else None
            end_date_str = extracted_data.end_date.isoformat() if extracted_data.end_date else None
            if start_date_str or end_date_str:
                self._date_intervals.add((start_date_str, end_date_str))

    def _process_original_dates(self, extracted_data: ExtractedData) -> None:
        """Process original date string fields."""
        if extracted_data.start_date_original or extracted_data.end_date_original:
            start_date_str_original = extracted_data.start_date_original if extracted_data.start_date_original else None
            end_date_str_original = extracted_data.end_date_original if extracted_data.end_date_original else None
            if start_date_str_original or end_date_str_original:
                self._date_intervals_original.add((start_date_str_original, end_date_str_original))

    def get_date_intervals(self) -> list[tuple[str | None, str | None]]:
        """Get list of parsed date intervals."""
        return list(self._date_intervals)

    def get_date_intervals_original(self) -> list[tuple[str | None, str | None]]:
        """Get list of original date intervals."""
        return list(self._date_intervals_original)
