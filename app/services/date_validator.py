from dataclasses import dataclass
import logging
from typing import cast

from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from config import settings
from constants.message import (
    EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT,
    EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT,
    EXTRACT_DATES_SYSTEM_PROMPT,
    EXTRACT_DATES_USER_PROMPT,
)
from repositories import OpenAIRepository
from schemas.dates import DatesLLMResponse


__all__ = ['DateValidatorService']


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class DateValidatorService:
    """Validates and extracts date information from user messages."""

    openai_service: OpenAIRepository
    temperature: float = settings.openai.default_temperature
    max_completion_tokens: int = settings.openai.max_completion_tokens

    @staticmethod
    def _create_system_message(message: str) -> ChatCompletionSystemMessageParam:
        """Create a system message for the chat completion."""
        return {'role': 'system', 'content': message}

    @staticmethod
    def _create_user_message(message: str) -> ChatCompletionUserMessageParam:
        """Create a user message for the chat completion."""
        return {'role': 'user', 'content': message}

    async def find_dates(
        self,
        user_message: str,
    ) -> DatesLLMResponse:
        """
        Validate the user's message dates using an LLM.

        Args:
            user_message: The user's message content.

        Returns:
            The validated dates.
        """

        # Prepare the messages
        system_message = self._create_system_message(EXTRACT_DATES_SYSTEM_PROMPT)
        user_message_param = self._create_user_message(
            EXTRACT_DATES_USER_PROMPT.format(
                user_message=user_message,
            )
        )

        # Call the OpenAI API
        response = await self.openai_service.generate_chat_completion(
            messages=[
                cast(ChatCompletionMessageParam, system_message),
                cast(ChatCompletionMessageParam, user_message_param),
            ],
            temperature=self.temperature,
            response_format=DatesLLMResponse,
        )

        if not isinstance(response, DatesLLMResponse):
            raise RuntimeError(f'Invalid response from model, expected {DatesLLMResponse}, got {type(response)}')

        return response

    async def date_is_text(self, user_message: str, date: str) -> bool:
        """
        Check if the message contains a date.
        """
        system_message = self._create_system_message(EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT)
        user_message_param = self._create_user_message(
            EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT.format(
                user_message=user_message,
                date=date,
            )
        )
        response = await self.openai_service.generate_text_completion(
            messages=[
                cast(ChatCompletionMessageParam, system_message),
                cast(ChatCompletionMessageParam, user_message_param),
            ],
            temperature=self.temperature,
            max_completion_tokens=self.max_completion_tokens,
        )

        # Parse the text response: "True", "False", or "Null"
        response_clean = response.strip().lower()
        return response_clean == 'true'
