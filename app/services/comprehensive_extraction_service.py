"""Comprehensive document extraction service for enhanced qual field processing."""

import logging
from typing import TYPE_CHECKING
from uuid import UUID

from repositories import ConversationMessageRepository, ExtractedDataRepository
from schemas import AggregatedData

if TYPE_CHECKING:
    from services.document import DocumentService
    from services.extracted_data import ExtractedDataService


__all__ = ['ComprehensiveExtractionService']


logger = logging.getLogger(__name__)


class ComprehensiveExtractionService:
    """
    Service for handling comprehensive document extraction and enhanced field processing.
    
    This service manages the collection of existing chunks from blob storage and triggers
    enhanced extraction workflows for comprehensive qual generation with all required fields.
    """

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        document_service: 'DocumentService',
        extracted_data_service: 'ExtractedDataService',
    ):
        """
        Initialize the comprehensive extraction service.
        
        Args:
            conversation_message_repository: Repository for conversation message operations
            document_service: Service for document and blob storage operations
            extracted_data_service: Service for extracted data aggregation and processing
        """
        self.conversation_message_repository = conversation_message_repository
        self.document_service = document_service
        self.extracted_data_service = extracted_data_service

    async def trigger_comprehensive_extraction(self, conversation_id: UUID) -> None:
        """
        Trigger comprehensive document processing for existing chunks.

        This method collects existing chunked data from previous Azure Durable Function
        executions and triggers enhanced field extraction for comprehensive qual generation.
        
        Args:
            conversation_id: UUID of the conversation to process
        """
        try:
            logger.info(f'Starting comprehensive extraction for conversation {conversation_id}')
            
            # Get existing chunks from blob storage for this conversation
            chunk_urls = await self.collect_existing_chunks(conversation_id)

            if not chunk_urls:
                logger.info(f'No existing chunks found for conversation {conversation_id}')
                return

            # Trigger enhanced extraction workflow
            await self.trigger_enhanced_extraction_workflow(conversation_id, chunk_urls)
            
            logger.info(f'Comprehensive extraction completed for conversation {conversation_id}')

        except Exception as e:
            logger.warning(f'Error in comprehensive extraction for conversation {conversation_id}: {e}')
            # Don't re-raise to avoid breaking the main qual generation flow

    async def collect_existing_chunks(self, conversation_id: UUID) -> list[str]:
        """
        Collect existing chunk URLs from blob storage for this conversation.

        This method searches through conversation messages to find processed documents
        and constructs the expected chunk URLs based on existing blob storage patterns.
        
        Args:
            conversation_id: UUID of the conversation to search
            
        Returns:
            List of chunk URLs from previous processing
        """
        try:
            logger.debug(f'Collecting existing chunks for conversation {conversation_id}')
            
            # Use document service to find existing chunks
            # This leverages the existing blob storage patterns
            chunk_urls = []

            # Get conversation messages to find processed documents
            conversation_messages = await self.conversation_message_repository.get_combined_history(
                conversation_id
            )

            for message in conversation_messages:
                if hasattr(message, 'documents') and message.documents:
                    for document in message.documents:
                        # Construct expected chunk paths based on existing patterns
                        # Chunks are stored at: chat-attachments/chunks/{original_filename_or_uuid}_chunk_{chunk_index}.json
                        message_id = str(message.public_id) if hasattr(message, 'public_id') else str(message.Id)

                        # Try to find chunks for this document
                        base_chunk_path = f'chat-attachments/chunks/{document.file_name}_chunk_'

                        # For now, assume up to 10 chunks per document (can be made dynamic)
                        for i in range(10):
                            chunk_url = (
                                f'https://{self.document_service.storage_account_name}.blob.core.windows.net/'
                                f'chat-attachments/chunks/{document.file_name}_chunk_{i}.json'
                            )

                            # Check if chunk exists (simplified - in production would verify existence)
                            chunk_urls.append(chunk_url)

            # Limit to prevent excessive processing
            limited_chunks = chunk_urls[:50]
            
            logger.debug(f'Found {len(limited_chunks)} potential chunks for conversation {conversation_id}')
            return limited_chunks

        except Exception as e:
            logger.warning(f'Error collecting existing chunks for conversation {conversation_id}: {e}')
            return []

    async def trigger_enhanced_extraction_workflow(self, conversation_id: UUID, chunk_urls: list[str]) -> None:
        """
        Trigger the enhanced extraction workflow using Azure Durable Functions.

        This method would integrate with Azure Durable Functions to perform specialized
        field extraction for comprehensive qual generation. Currently simulates the workflow.
        
        Args:
            conversation_id: UUID of the conversation being processed
            chunk_urls: List of chunk URLs to process
        """
        try:
            logger.info(f'Triggering enhanced extraction workflow for conversation {conversation_id}')
            
            # This would integrate with the Azure Durable Functions workflow
            # For now, we'll log the intent and prepare for future integration
            logger.info(
                f'Would trigger enhanced extraction for {len(chunk_urls)} chunks for conversation {conversation_id}'
            )

            # In a full implementation, this would:
            # 1. Call the summarize_other_field_activity to aggregate 'other' content
            # 2. Call the enhanced_extraction_activity for specialized field extraction
            # 3. Update the extracted data with comprehensive field information

            # For now, we'll simulate the enhanced extraction by updating existing data
            await self.simulate_enhanced_extraction(conversation_id)

        except Exception as e:
            logger.warning(f'Error triggering enhanced extraction workflow for conversation {conversation_id}: {e}')

    async def simulate_enhanced_extraction(self, conversation_id: UUID) -> None:
        """
        Simulate enhanced extraction by updating existing extracted data.

        This is a placeholder for the full Azure Durable Functions integration.
        In production, this would be replaced by actual calls to Azure Durable Functions
        activities for comprehensive field extraction.
        
        Args:
            conversation_id: UUID of the conversation being processed
        """
        try:
            logger.debug(f'Simulating enhanced extraction for conversation {conversation_id}')
            
            # Get existing aggregated data
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

            # Log the comprehensive extraction trigger
            logger.info(f'Comprehensive extraction triggered for conversation {conversation_id}')
            logger.info(
                f'Current aggregated data includes {len(aggregated_data.client_name)} client names, '
                f'{len(aggregated_data.ldmf_country)} countries, {len(aggregated_data.date_intervals)} date intervals'
            )

            # Log comprehensive field status
            self._log_comprehensive_field_status(conversation_id, aggregated_data)

            # The actual enhanced extraction would happen in Azure Durable Functions
            # and would populate the new fields we've added to the schema

        except Exception as e:
            logger.warning(f'Error in simulated enhanced extraction for conversation {conversation_id}: {e}')

    def _log_comprehensive_field_status(self, conversation_id: UUID, aggregated_data: AggregatedData) -> None:
        """
        Log the status of comprehensive fields for debugging and monitoring.
        
        Args:
            conversation_id: UUID of the conversation
            aggregated_data: Current aggregated data to analyze
        """
        try:
            # Log engagement description fields
            engagement_desc_fields = [
                aggregated_data.business_issues,
                aggregated_data.scope_approach,
                aggregated_data.value_delivered,
                aggregated_data.engagement_summary,
                aggregated_data.one_line_description,
            ]
            populated_desc_fields = sum(1 for field in engagement_desc_fields if field)
            
            # Log engagement details fields
            engagement_detail_fields = [
                aggregated_data.client_references,
                aggregated_data.client_name_sharing,
                aggregated_data.client_industry,
                aggregated_data.engagement_dates,
                aggregated_data.engagement_locations,
                aggregated_data.engagement_fee_display,
                aggregated_data.client_services,
                aggregated_data.source_of_work,
            ]
            populated_detail_fields = sum(1 for field in engagement_detail_fields if field)
            
            # Log usage & team fields
            usage_team_fields = [
                aggregated_data.qual_usage,
                aggregated_data.team_roles,
                aggregated_data.approver,
            ]
            populated_usage_fields = sum(1 for field in usage_team_fields if field)
            
            logger.info(
                f'Comprehensive field status for conversation {conversation_id}: '
                f'Description fields: {populated_desc_fields}/5, '
                f'Detail fields: {populated_detail_fields}/8, '
                f'Usage/Team fields: {populated_usage_fields}/3'
            )
            
        except Exception as e:
            logger.debug(f'Error logging comprehensive field status for conversation {conversation_id}: {e}')

    async def get_extraction_status(self, conversation_id: UUID) -> dict[str, any]:
        """
        Get the current status of comprehensive extraction for a conversation.
        
        Args:
            conversation_id: UUID of the conversation
            
        Returns:
            Dictionary containing extraction status information
        """
        try:
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)
            
            return {
                'conversation_id': str(conversation_id),
                'core_fields_populated': not aggregated_data.is_empty,
                'comprehensive_fields_populated': aggregated_data.comprehensive_fields_populated,
                'client_names_count': len(aggregated_data.client_name),
                'countries_count': len(aggregated_data.ldmf_country),
                'date_intervals_count': len(aggregated_data.date_intervals),
                'has_business_issues': bool(aggregated_data.business_issues),
                'has_scope_approach': bool(aggregated_data.scope_approach),
                'has_value_delivered': bool(aggregated_data.value_delivered),
                'has_engagement_summary': bool(aggregated_data.engagement_summary),
                'has_client_references': bool(aggregated_data.client_references),
                'has_team_roles': bool(aggregated_data.team_roles),
            }
            
        except Exception as e:
            logger.warning(f'Error getting extraction status for conversation {conversation_id}: {e}')
            return {
                'conversation_id': str(conversation_id),
                'error': str(e),
                'status': 'error'
            }
