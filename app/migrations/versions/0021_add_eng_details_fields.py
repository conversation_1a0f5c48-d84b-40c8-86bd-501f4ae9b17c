"""add eng details fields

Revision ID: 0021
Revises: 0020
Create Date: 2025-07-16 17:05:40.582999

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0021'
down_revision: Union[str, None] = '0020'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualExtractedData', sa.Column('business_issues', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('scope_approach', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('value_delivered', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('engagement_summary', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('one_line_description', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('client_references', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('client_name_sharing', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('client_industry', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('engagement_dates', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('engagement_locations', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('engagement_fee_display', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('client_services', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('source_of_work', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('qual_usage', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('team_roles', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('approver', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualExtractedData', 'approver')
    op.drop_column('QualExtractedData', 'team_roles')
    op.drop_column('QualExtractedData', 'qual_usage')
    op.drop_column('QualExtractedData', 'source_of_work')
    op.drop_column('QualExtractedData', 'client_services')
    op.drop_column('QualExtractedData', 'engagement_fee_display')
    op.drop_column('QualExtractedData', 'engagement_locations')
    op.drop_column('QualExtractedData', 'engagement_dates')
    op.drop_column('QualExtractedData', 'client_industry')
    op.drop_column('QualExtractedData', 'client_name_sharing')
    op.drop_column('QualExtractedData', 'client_references')
    op.drop_column('QualExtractedData', 'one_line_description')
    op.drop_column('QualExtractedData', 'engagement_summary')
    op.drop_column('QualExtractedData', 'value_delivered')
    op.drop_column('QualExtractedData', 'scope_approach')
    op.drop_column('QualExtractedData', 'business_issues')
    # ### end Alembic commands ###
