{"Intentions": [{"intentionName": "undefined", "description": "A user asks for something completely unrelated to qualitative analysis, project work, business consulting, or the current conversation context. This should only be used for clearly off-topic requests.", "userMessageExamples": ["How can I add a user to my ClickUp account?", "What is the best basketball player in US?", "I really need to update my instagram profile image, can you help?", "Can you help me with my math homework?", "What's the weather like today?", "How do I cook pasta?", "What's the capital of France?"]}, {"intentionName": "generate_qual", "description": "The user asks to create or generate a qualitative analysis (qual) based on data, project or other information. All data is provided in the prompt.", "userMessageExamples": ["Please generate a qual based on this project", "Can you create a qual for these survey results?"]}, {"intentionName": "extraction", "description": "User mentions a client name, dates, member firm, objectives or results.", "userMessageExamples": ["Client name is Deloitte US", "Our sales increased by 15% in April 2023.", "We're helping Acme Inc. with their digital", "Well the goals were much higher than the reality we've got", "This time we focused on Canadian department, instead of Taiwanese", "Write up a qual for my recent project. We advised the client on how to develop and implement automation technology.", "Use this case study deck to write a qual for an engagement where we assisted the client with a large business process outsourcing program for an Automotive Captive.", "Attached is a proposal where Deloitte provided a strategic vision for a health care faciltity. Use this to start a qual.", "Based on this value delivered write-up, create a new qual for my client, Mercedes Benz. Deloitte explored and did end-to-end pilot for testing automation for 12 scenarios and 200 steps with 10 sub-processes.We supported the client for actively driving forward the transition from automotive manufacturer to the networked mobility service provider. They changed their business model from wholesale of the vehicle to dealer and then dealer to final customer to directly selling their products to the end customers. Benefits: this digital transformation enabled them to have full control on customer journey instead of relying on dealers and franchise partners thus minimizing discrepancies in pricing, stock, data, and CRM. The role of the dealerships was transformed from retailers to facilitators thus ensuring strict adherence to process and controls and minimizing process variables by ensuring use of their globally approved system components. The control test automation enabled the client to reduce time spent on manually testing applications and error prone manual data migration during rollout thus ensuring a faster and seamless transition", "Expand on the business issue described below. SAMA created a special purpose entity to manage and operate the payment systems (wholesale and retail) under its ambit. In order to ensure a smooth process and workforce transition and efficient operations of the newly created entity, SAMA (Saudi Payments) required support in designing the most critical layers of the new target operating model. Check tense and grammar"]}, {"intentionName": "example", "description": "A user asks for an example, a template or a demonstration of something. This can be a direct request to show somthing, or a question about what datapoints to include", "userMessageExamples": ["Show me an example prompt", "Can you give me an example prompt?", "Show me a template for data analysis", "I don't get how should I approach this task. Can you give me an example of these fields filled in with come real project info?"]}, {"intentionName": "dash_discard", "description": "The user wants to reject, ignore or discard certain information on dashboard(selecting dash task) and instead wants to provide data in prompts.", "userMessageExamples": ["Discard this analysis please", "I dont need this section, remove it", "No, skip tasks", "No, create new qual", "No, I don't want to use dash tasks", "No, skip the dash tasks", "No, create new qual without dash task", "I don't want to use this dash task information", "Skip this dash task and let me provide my own data", "Discard the dash task details", "No, I want to start fresh without dash tasks", "Remove this dash task information"]}, {"intentionName": "uncertainty", "description": "A user expresses uncertainty about what to do next. This can be a request for help, a question about the next steps, or a general inquiry about the process.", "userMessageExamples": ["I'm new here, what should I write?", "Not sure what to do", "How do I create a qual?", "What information do you need?", "What do i do next? Can I use this bot as my personal assistant, for example?", "Are you capable of creating my report?", "Can you even act as a report generator?"]}, {"intentionName": "need_context", "description": "A user wants to work with some data. It can be a request for a description, request to generate Qual or a request to provide any information about the project. As the user did not provide any information about the project, this request will need more context.", "userMessageExamples": ["Write a brief description of my project", "Please generate a qual based on this project", "Could you provide a brief summary of what i've done?", "What client is my report about?"]}, {"intentionName": "user_confirmation", "description": "A user provides active agreement with previous question. Also includes responses indicating the user wants to proceed without adding more information.", "userMessageExamples": ["Yes", "Yes, this is correct", "Yes, this is the correct information", "Yep", "Correct", "That's right", "Affirmative", "You got it", "Spot on", "Precisely", "Exactly", "Absolutely", "Totally", "That's accurate", "Correct statement", "I agree", "Indeed", "Nothing", "Nothing else", "No", "Nope", "That's all", "I'm good", "No additional information", "Nothing more", "That's everything"]}, {"intentionName": "change_engagement_dates", "description": "A user wants to change engagement dates.", "userMessageExamples": ["I need to change the project start date.", "Can we reschedule the engagement for next month?", "The client wants to push back the deadline to Friday.", "Could you update the end date of this project?", "We need to adjust the timeline for this engagement.", "Is it possible to move the kickoff meeting to a later date?", "I'd like to change the dates for this report.", "The project dates need to be modified.", "Can we extend the engagement period?", "I want to change when this engagement is scheduled to end."]}]}