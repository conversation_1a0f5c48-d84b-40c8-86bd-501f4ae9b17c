import sqlalchemy as sa
from sqlalchemy.orm import relationship

from constants.extracted_data import DataSourceType
from core.db import Base


__all__ = ['QualExtractedData']


class QualExtractedData(Base):
    __tablename__ = 'QualExtractedData'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    QualConversationId = sa.Column(sa.Integer, sa.<PERSON>Key('QualConversation.Id'), nullable=False)
    DataSourceType = sa.Column(sa.Enum(DataSourceType, values_callable=lambda x: [e.value for e in x]), nullable=False)
    CreatedAt = sa.Column(sa.DateTime, server_default=sa.func.now(), nullable=False)

    # Existing core fields
    ActivityId = sa.Column(sa.Integer, nullable=True, index=True)
    ActivityName = sa.Column(sa.UnicodeText, nullable=True)
    ClientName = sa.Column(sa.UnicodeText, nullable=True)
    LDMFCountry = sa.Column(sa.UnicodeText, nullable=True)
    Title = sa.Column(sa.UnicodeText, nullable=True)
    StartDate = sa.Column(sa.Date, nullable=True)
    EndDate = sa.Column(sa.Date, nullable=True)
    StartDateOriginal = sa.Column(sa.UnicodeText, nullable=True)
    EndDateOriginal = sa.Column(sa.UnicodeText, nullable=True)
    Industries = sa.Column(sa.UnicodeText, nullable=True)
    Services = sa.Column(sa.UnicodeText, nullable=True)
    Roles = sa.Column(sa.UnicodeText, nullable=True)
    ObjectiveAndScope = sa.Column(sa.UnicodeText, nullable=True)
    Outcomes = sa.Column(sa.UnicodeText, nullable=True)

    # Engagement Description fields
    business_issues = sa.Column(sa.UnicodeText, nullable=True)
    scope_approach = sa.Column(sa.UnicodeText, nullable=True)
    value_delivered = sa.Column(sa.UnicodeText, nullable=True)
    engagement_summary = sa.Column(sa.UnicodeText, nullable=True)
    one_line_description = sa.Column(sa.UnicodeText, nullable=True)

    # Engagement Details fields
    client_references = sa.Column(sa.UnicodeText, nullable=True)
    client_name_sharing = sa.Column(sa.UnicodeText, nullable=True)
    client_industry = sa.Column(sa.UnicodeText, nullable=True)
    engagement_dates = sa.Column(sa.UnicodeText, nullable=True)
    engagement_locations = sa.Column(sa.UnicodeText, nullable=True)
    engagement_fee_display = sa.Column(sa.UnicodeText, nullable=True)
    client_services = sa.Column(sa.UnicodeText, nullable=True)
    source_of_work = sa.Column(sa.UnicodeText, nullable=True)

    # Usage & Team fields
    qual_usage = sa.Column(sa.UnicodeText, nullable=True)
    team_roles = sa.Column(sa.UnicodeText, nullable=True)
    approver = sa.Column(sa.UnicodeText, nullable=True)

    Conversation = relationship('QualConversation', back_populates='ExtractedData')

    __table_args__ = (sa.UniqueConstraint('QualConversationId', 'DataSourceType', name='uq_conversation_source_type'),)
