import logging

import azure.durable_functions as df

from constants.extracted_data import DataSourceType
from durable_functions.activities.models import (
    AggregateMultiSourceDataActivityInput,
    SaveAggregatedResultsToBlobActivityInput,
    SaveExtractionDataActivityInput,
    SendFinalQueueMessageActivityInput,
    SendNotificationActivityInput,
    UpdateProcessingStatusActivityInput,
)
from durable_functions.utils import (
    ActivityName,
    EventType,
    OrchestratorInputType,
    OrchestratorName,
    ProcessingStatus,
    parse_blob_url,
)
from durable_functions.utils.extracted_data_merger import ExtractedDataMerger
from durable_functions.utils.models import FinalExtractionDataResults

from .models import (
    UnifiedProcessingInput,
    UnifiedProcessingOutput,
    UnifiedProcessingOutputFailed,
)


logger = logging.getLogger(__name__)
bp = df.Blueprint()


@bp.orchestration_trigger('context', OrchestratorName.UnifiedProcessing)
def unified_processing_orchestrator(context: df.DurableOrchestrationContext):
    """
    Orchestrator function for unified processing of text prompts and documents.

    This orchestrator coordinates parallel processing of multiple sources:
    1. Process text prompts and documents in parallel using existing orchestrators
    2. Aggregate results from all sources with proper field-specific merging
    3. Save aggregated results to blob storage
    4. Send final queue message for further processing

    Args:
        context: Durable orchestration context

    Returns:
        Dictionary containing the unified processing results
    """

    input_dict = context.get_input()
    logger.info(f'Starting unified processing with input: {input_dict}')

    try:
        input_data = UnifiedProcessingInput.model_validate(input_dict)

        # Extract message_id from the first available source
        message_id = None
        if input_data.text_prompt:
            message_id, _ = parse_blob_url(input_data.text_prompt, is_prompt=True)
        elif input_data.documents:
            message_id, _ = parse_blob_url(input_data.documents[0])

        if not message_id:
            raise ValueError('No valid sources provided for processing')

        logger.info(f'Processing unified message for message_id: {message_id}')

        # Update status to indicate unified processing started
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.UnifiedProcessingStarted,
                message='Starting unified processing of text and documents',
            ),
        )

        # Send notification
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.UnifiedProcessingStarted,
                data={
                    'message_id': message_id,
                    'text_prompt': input_data.text_prompt,
                    'documents': input_data.documents,
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Start parallel processing tasks
        parallel_tasks = []
        source_types = []

        # Process text prompt if present
        if input_data.text_prompt:
            prompt_input = {
                'prompt_url': input_data.text_prompt,
                'type': OrchestratorInputType.Prompt,
                'signalr_user_id': input_data.signalr_user_id,
                'is_part_of_unified_processing': True,
            }
            parallel_tasks.append(context.call_sub_orchestrator(OrchestratorName.DocumentProcessing, prompt_input))
            source_types.append(DataSourceType.PROMPT)

        # Process documents if present (max 3 files)
        if input_data.documents:
            for document_url in input_data.documents[:3]:  # Limit to max 3 files
                document_input = {
                    'blob_url': document_url,
                    'type': OrchestratorInputType.Document,
                    'signalr_user_id': input_data.signalr_user_id,
                    'is_part_of_unified_processing': True,
                }
                parallel_tasks.append(
                    context.call_sub_orchestrator(OrchestratorName.DocumentProcessing, document_input)
                )
                source_types.append(DataSourceType.DOCUMENTS)

        # Wait for all parallel processing to complete
        processing_results = yield context.task_all(parallel_tasks)

        logger.info(f'Parallel processing completed. Results: {len(processing_results)}')

        # Pre-aggregate multiple document results before saving to database
        # This fixes the issue where multiple documents overwrite each other due to unique constraint
        document_results = []
        prompt_result = None
        merged_document_result = None  # Initialize to None

        for i, results in enumerate(processing_results):
            source_type = source_types[i]
            if source_type == DataSourceType.DOCUMENTS:
                # Collect document results for pre-aggregation
                document_results.append(FinalExtractionDataResults.model_validate(results['metadata']))
            elif source_type == DataSourceType.PROMPT:
                prompt_result = FinalExtractionDataResults.model_validate(results['metadata'])

        # Pre-aggregate all document results into a single merged result
        if document_results:
            logger.info(f'Pre-aggregating {len(document_results)} document results')
            # Use the existing merger to combine multiple document results
            document_source_results = [(DataSourceType.DOCUMENTS, result) for result in document_results]
            merged_document_result = ExtractedDataMerger.merge_multi_source_results(document_source_results)

            # Save the merged document result to database
            yield context.call_activity(
                ActivityName.SaveExtractionData,
                SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=merged_document_result,
                    data_source_type=DataSourceType.DOCUMENTS,
                ),
            )
            logger.info(f'Saved merged document results: {merged_document_result}')

        # Save prompt result if it exists (since individual orchestrator skips saving when part of unified processing)
        if prompt_result:
            yield context.call_activity(
                ActivityName.SaveExtractionData,
                SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=prompt_result,
                    data_source_type=DataSourceType.PROMPT,
                ),
            )
            logger.info(f'Saved prompt results: {prompt_result}')

        # Prepare source results for final aggregation
        # Now we can safely retrieve from database since we've saved both merged document and prompt results
        source_results_for_aggregation = []
        if prompt_result:
            source_results_for_aggregation.append((DataSourceType.PROMPT, prompt_result.model_dump()))
        if (
            document_results and merged_document_result
        ):  # Only add if we had documents to process and merged_document_result is not None
            source_results_for_aggregation.append((DataSourceType.DOCUMENTS, merged_document_result.model_dump()))

        # Aggregate results from all sources
        aggregated_results: FinalExtractionDataResults = yield context.call_activity(
            ActivityName.AggregateMultiSourceData,
            AggregateMultiSourceDataActivityInput(
                message_id=message_id,
                source_results=source_results_for_aggregation,
            ),
        )

        logger.info(f'Data aggregation completed: {aggregated_results}')

        # Update status after aggregation
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.MultiSourceDataAggregated,
                message='Multi-source data aggregation completed',
                metadata={'aggregated_results': aggregated_results.model_dump()},
            ),
        )

        # Send notification for aggregation completion
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.MultiSourceDataAggregated,
                data={
                    'message_id': message_id,
                    'aggregated_results': aggregated_results.model_dump(),
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Send RequiredFieldsExtracted event for unified processing
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.RequiredFieldsExtracted,
                data={
                    'message_id': message_id,
                    'unified_processing': True,
                    'results': aggregated_results.model_dump(),
                    'sources': {
                        'text_prompt': input_data.text_prompt,
                        'documents': input_data.documents,
                    },
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Save aggregated results to blob storage
        blob_url: str = yield context.call_activity(
            ActivityName.SaveAggregatedResultsToBlob,
            SaveAggregatedResultsToBlobActivityInput(
                message_id=message_id,
                aggregated_data=aggregated_results,
            ),
        )

        # Update status after saving to blob
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.FinalResultsSavedToBlob,
                message='Final results saved to blob storage',
                metadata={'blob_url': blob_url},
            ),
        )

        # Send final queue message for further processing
        yield context.call_activity(
            ActivityName.SendFinalQueueMessage,
            SendFinalQueueMessageActivityInput(
                message_id=message_id,
                blob_url=blob_url,
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Update final status
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.UnifiedProcessingCompleted,
                message='Unified processing completed successfully',
                metadata={
                    'blob_url': blob_url,
                    'aggregated_results': aggregated_results.model_dump(),
                },
            ),
        )

        # Send final notification
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.UnifiedProcessingCompleted,
                data={
                    'message_id': message_id,
                    'blob_url': blob_url,
                    'aggregated_results': aggregated_results.model_dump(),
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Return success response
        response = UnifiedProcessingOutput.model_validate(
            {
                'message_id': message_id,
                'status': ProcessingStatus.UnifiedProcessingCompleted,
                'aggregated_results': aggregated_results.model_dump(),
                'blob_url': blob_url,
                'signalr_user_id': input_data.signalr_user_id,
            }
        ).model_dump()

        return response

    except Exception as e:
        logger.exception('Error in unified processing orchestrator')

        # Try to extract message_id for error reporting
        safe_message_id = None
        safe_signalr_user_id = None
        try:
            error_input_data = UnifiedProcessingInput.model_validate(input_dict)
            safe_signalr_user_id = error_input_data.signalr_user_id
            if error_input_data.text_prompt:
                safe_message_id, _ = parse_blob_url(error_input_data.text_prompt, is_prompt=True)
            elif error_input_data.documents:
                safe_message_id, _ = parse_blob_url(error_input_data.documents[0])
        except Exception:
            pass

        # Send error notifications if we have a message ID
        if safe_message_id and safe_signalr_user_id:
            try:
                yield context.call_activity(
                    ActivityName.UpdateProcessingStatus,
                    UpdateProcessingStatusActivityInput(
                        message_id=safe_message_id,
                        status=ProcessingStatus.UnifiedProcessingFailed,
                        message=f'Unified processing failed: {str(e)}',
                    ),
                )

                yield context.call_activity(
                    ActivityName.SendNotification,
                    SendNotificationActivityInput(
                        event_type=EventType.UnifiedProcessingFailed,
                        data={
                            'message_id': safe_message_id,
                            'error': str(e),
                        },
                        signalr_user_id=safe_signalr_user_id,
                    ),
                )
            except Exception:
                logger.exception('Error sending failure notifications')

        # Return error response
        error_response = UnifiedProcessingOutputFailed.model_validate(
            {
                'message_id': safe_message_id,
                'status': ProcessingStatus.UnifiedProcessingFailed,
                'error': str(e),
                'signalr_user_id': safe_signalr_user_id or 'unknown',
            }
        ).model_dump()

        return error_response
