import logging

from openai import AsyncAzureOpenAI
from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from constants.message import EXTRACT_DATA_SYSTEM_PROMPT, EXTRACT_DATA_USER_PROMPT
from durable_functions.application.config import settings
from durable_functions.utils.models import LLMExtractedDataResult


__all__ = ['OpenAIRepository']


logger = logging.getLogger(__name__)

openai_client = AsyncAzureOpenAI(
    api_key=settings.openai.key,
    api_version=settings.openai.api_version,
    azure_endpoint=settings.openai.endpoint,
)


class OpenAIRepository:
    """Repository for interacting with Azure OpenAI API."""

    def __init__(self, client: AsyncAzureOpenAI = openai_client) -> None:
        """Initialize the OpenAI service with settings from the config."""
        self.client = client

    async def extract_data(
        self,
        text: str,
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> LLMExtractedDataResult:
        logger.info(f'Calling extract_data with text: "{text}", temperature: {temperature}, model: "{model}"')
        try:
            messages: list[ChatCompletionMessageParam] = [
                ChatCompletionSystemMessageParam(role='system', content=EXTRACT_DATA_SYSTEM_PROMPT),
                ChatCompletionUserMessageParam(role='user', content=EXTRACT_DATA_USER_PROMPT.format(text=text)),
            ]
            logger.info(f'Input messages to OpenAI: {messages}')
            completion = await self.client.beta.chat.completions.parse(
                model=model,
                messages=messages,
                response_format=LLMExtractedDataResult,
                temperature=temperature,
            )
            message = completion.choices[0].message
            if message.refusal:
                logger.exception('OpenAI refused to extract data: %s', message.refusal)
                return LLMExtractedDataResult()

            if message.parsed is None:
                logger.exception('OpenAI returned no parsed data for text: %s', text)
                return LLMExtractedDataResult()

            logger.info(f'Extracted data in AI: {message.parsed}')
            return message.parsed
        except Exception:
            logger.exception('Error extracting data')
            return LLMExtractedDataResult()
