"""Enhanced extraction activities for comprehensive qual field processing."""

import asyncio
import json
import logging
from typing import Any

import azure.durable_functions as df
from azure.storage.blob.aio import BlobServiceClient

from config.app_settings import settings
from durable_functions.utils.models import FinalExtractionDataResults, LLMExtractedDataResult
from repositories import OpenAIRepository

from .models import (
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    SummarizeOtherFieldActivityInput,
    SummarizeOtherFieldActivityOutput,
)


logger = logging.getLogger(__name__)

bp = df.Blueprint()


@bp.activity_trigger(input_name='input_data')
async def summarize_other_field_activity(input_data: dict[str, Any]) -> dict[str, Any]:
    """
    Activity to summarize 'other' field content across all chunks for enhanced extraction.
    
    Args:
        input_data: Dictionary containing conversation_id and chunk_urls
        
    Returns:
        Dictionary containing summarized other field content
    """
    try:
        activity_input = SummarizeOtherFieldActivityInput.model_validate(input_data)
        logger.info(f'Starting other field summarization for conversation {activity_input.conversation_id}')

        # Initialize blob client
        blob_service_client = BlobServiceClient(
            account_url=f"https://{settings.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net",
            credential=settings.AZURE_STORAGE_ACCOUNT_KEY,
        )

        other_contents = []
        
        # Collect 'other' field content from all chunks
        for chunk_url in activity_input.chunk_urls:
            try:
                # Parse blob URL to get container and blob name
                url_parts = chunk_url.replace(f"https://{settings.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/", "").split("/", 1)
                container_name = url_parts[0]
                blob_name = url_parts[1]
                
                # Download chunk content
                blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
                blob_content = await blob_client.download_blob()
                chunk_data = json.loads(await blob_content.readall())
                
                # Extract 'other' field if present
                if 'other' in chunk_data and chunk_data['other']:
                    other_contents.append(chunk_data['other'])
                    
            except Exception as e:
                logger.warning(f'Error processing chunk {chunk_url}: {e}')
                continue

        # Combine all other content
        combined_other_content = ' | '.join(other_contents) if other_contents else None
        
        result = SummarizeOtherFieldActivityOutput(
            conversation_id=activity_input.conversation_id,
            summarized_other_content=combined_other_content,
            chunk_count=len(activity_input.chunk_urls),
            processed_chunks=len([c for c in other_contents if c])
        )
        
        logger.info(f'Completed other field summarization: {result.processed_chunks}/{result.chunk_count} chunks processed')
        return result.model_dump()

    except Exception as e:
        logger.exception(f'Error in summarize_other_field_activity: {e}')
        raise


@bp.activity_trigger(input_name='input_data')
async def enhanced_extraction_activity(input_data: dict[str, Any]) -> dict[str, Any]:
    """
    Activity to perform enhanced field extraction using specialized LLM prompts.
    
    Args:
        input_data: Dictionary containing summarized content and target fields
        
    Returns:
        Dictionary containing extracted field data
    """
    try:
        activity_input = EnhancedExtractionActivityInput.model_validate(input_data)
        logger.info(f'Starting enhanced extraction for conversation {activity_input.conversation_id}')

        if not activity_input.summarized_content:
            logger.warning('No summarized content provided for enhanced extraction')
            return EnhancedExtractionActivityOutput(
                conversation_id=activity_input.conversation_id,
                extracted_fields={}
            ).model_dump()

        # Initialize OpenAI repository
        openai_repo = OpenAIRepository()
        
        # Create specialized prompt for enhanced field extraction
        system_prompt = """You are an expert at extracting detailed engagement information from business documents and descriptions. 
        Extract the following fields from the provided content. If a field is not present or cannot be determined, return null.
        
        Focus on extracting:
        - business_issues: Key business challenges or problems addressed
        - scope_approach: Methodology, approach, or scope of work undertaken  
        - value_delivered: Outcomes, benefits, or value created for the client
        - engagement_summary: Overall summary of the engagement
        - one_line_description: Concise one-line description of the work
        - client_references: Any client testimonials, quotes, or references
        - client_name_sharing: Information about client name usage permissions
        - client_industry: Industry or sector of the client
        - engagement_locations: Geographic locations where work was performed
        - engagement_fee_display: Fee information or engagement value
        - client_services: Services provided to the client
        - source_of_work: How the engagement was sourced or originated
        - qual_usage: Intended usage or purpose of this qualification
        - team_roles: Team members and their roles in the engagement
        - approver: Person who approved or signed off on the engagement
        
        Return the results in JSON format with the field names as keys."""

        user_prompt = f"Extract detailed engagement information from the following content:\n\n{activity_input.summarized_content}"

        # Call OpenAI for enhanced extraction
        response = await openai_repo.get_structured_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            response_format=LLMExtractedDataResult
        )

        # Extract the enhanced fields from response
        extracted_fields = {}
        if response:
            for field_name in activity_input.target_fields:
                if hasattr(response, field_name):
                    field_value = getattr(response, field_name)
                    if field_value and field_value.strip():
                        extracted_fields[field_name] = field_value.strip()

        result = EnhancedExtractionActivityOutput(
            conversation_id=activity_input.conversation_id,
            extracted_fields=extracted_fields
        )
        
        logger.info(f'Completed enhanced extraction: {len(extracted_fields)} fields extracted')
        return result.model_dump()

    except Exception as e:
        logger.exception(f'Error in enhanced_extraction_activity: {e}')
        raise
