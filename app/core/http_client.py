import logging
from typing import Callable

from backoff import expo, on_exception, types
from fastapi import status
import httpx

from config import Settings


__all__ = ['CustomAsyncClient']


class CustomAsyncClient(httpx.AsyncClient):
    logger = logging.getLogger(f'{__name__}.{__qualname__}')

    def __init__(self, *args, settings: Settings, raise_for_status: bool = True, **kwargs):
        kw_args = {
            'timeout': httpx.Timeout(timeout=settings.http_client.timeout),
            'follow_redirects': settings.http_client.follow_redirects,
            'verify': settings.http_client.verify_ssl,
            'limits': httpx.Limits(
                max_connections=settings.http_client.max_connections,
                max_keepalive_connections=settings.http_client.max_keepalive_connections,
            ),
        }
        kw_args.update(kwargs)
        super().__init__(*args, **kw_args)
        self._settings = settings
        self._raise_for_status = raise_for_status
        self._backoff_wrapped_request = self._get_backoff_wrapped_request()

    async def request(self, *args, **kwargs) -> httpx.Response:
        return await self._backoff_wrapped_request(*args, **kwargs)

    def _get_backoff_wrapped_request(self) -> Callable:
        return on_exception(
            expo,
            (httpx.HTTPStatusError, httpx.RequestError),
            max_tries=self._settings.http_client.backoff.max_retries,
            giveup=self._should_givup,
            on_giveup=self._log_giveup,
            logger=None,
            base=self._settings.http_client.backoff.backoff_base,
            factor=self._settings.http_client.backoff.backoff_factor,
        )(self._request)

    async def _request(self, *args, **kwargs) -> httpx.Response:
        response = await super().request(*args, **kwargs)
        if self._raise_for_status:
            if response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
                response.status_code = status.HTTP_503_SERVICE_UNAVAILABLE
            response.raise_for_status()
        return response

    @staticmethod
    def _should_givup(exception: Exception) -> bool:
        """
        Determine if we should give up for a given exception.

        Args:
            exception: The exception to check

        Returns:
            bool: True if we should retry, False otherwise
        """
        if isinstance(exception, httpx.HTTPStatusError):
            # Retry on specific HTTP status codes
            return exception.response.status_code not in {
                status.HTTP_408_REQUEST_TIMEOUT,
                status.HTTP_429_TOO_MANY_REQUESTS,
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                status.HTTP_502_BAD_GATEWAY,
                status.HTTP_503_SERVICE_UNAVAILABLE,
                status.HTTP_504_GATEWAY_TIMEOUT,
            }

        # Retry on request errors (connection issues, etc.)
        return not isinstance(exception, httpx.RequestError)

    @classmethod
    def _log_giveup(cls, details: types.Details) -> None:
        """Log giveups."""
        target = details['target'].__qualname__
        args = details['args'][1:]
        error = details['exception']  # pyright: ignore[reportGeneralTypeIssues]
        error_type = type(error)
        cls.logger.warning(
            'Giving up %s%s for the error "%s.%s: %s"', target, args, error_type.__module__, error_type.__name__, error
        )
