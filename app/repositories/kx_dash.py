import logging
from typing import Any, Dict, List

from dateutil.parser import parse

from config import settings
from constants.environment import Environment
from core.http_client import CustomAsyncClient
from core.urls import url_join


__all__ = ['KXDashRepository']


logger = logging.getLogger(__name__)


class KXDashRepository:
    """Repository for KX Dash API operations."""

    _NEED_LLM_DATE_VALIDATION_KEY = 'needLLMDateValidation'

    MOCKED_TASKS: Dict[int, Dict[str, Any]] = {
        100001: {
            'activityId': 100001,
            'activityName': 'Quality Review - Late Due Date',
            'activityType': 'Contribute engagement',
            'clientName': 'TechCorp Industries',
            'memberFirm': 'US',
            'country': 'United States',
            'globalBusiness': 'Audit & Assurance',
            'globalBusinessServiceArea': 'Audit',
            'globalBusinessServiceLine': 'External Audit',
            'globalIndustry': 'Technology, Media & Telecommunications',
            'globalIndustrySector': 'Software & Computer Services',
            'engagementCode': 'ENG-100001',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '01/01/2024',
            'engagementEndDate': '31/12/2024',
            'dueDate': '2024-12-30',  # Latest due date
            'status': 'Not Started',
            'needLLMDateValidation': False,
        },
        100002: {
            'activityId': 100002,
            'activityName': 'Risk Assessment - Middle Due Date',
            'activityType': 'Contribute qual',
            'clientName': 'Global Financial Solutions Ltd',
            'memberFirm': 'UK',
            'country': 'United Kingdom',
            'globalBusiness': 'Risk Advisory',
            'globalBusinessServiceArea': 'Risk & Financial Advisory',
            'globalBusinessServiceLine': 'Regulatory & Legal Support',
            'globalIndustry': 'Financial Services',
            'globalIndustrySector': 'Investment Banking',
            'engagementCode': 'ENG-100002',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': 'March 10',
            'engagementEndDate': 'September 30, 2024',
            'dueDate': '2024-06-15',  # Middle due date
            'status': 'In Progress',
            'needLLMDateValidation': False,
        },
        100003: {
            'activityId': 100003,
            'activityName': 'Compliance Review - Should be Filtered',
            'activityType': 'Contribute qual',
            'clientName': 'Manufacturing Corp',
            'memberFirm': 'CA',
            'country': 'Canada',
            'globalBusiness': 'Audit & Assurance',
            'globalBusinessServiceArea': 'Audit',
            'globalBusinessServiceLine': 'Internal Audit',
            'globalIndustry': 'Manufacturing',
            'globalIndustrySector': 'Industrial Manufacturing',
            'engagementCode': 'ENG-100003',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': 'Februart 1, 2024',
            'engagementEndDate': 'November 30, 2024',
            'dueDate': '2024-05-01',  # Early due date - will be filtered out
            'status': 'Not Started',
            'needLLMDateValidation': True,
        },
        100004: {
            'activityId': 100004,
            'activityName': 'Tax Advisory - Earliest Due Date',
            'activityType': 'Contribute qual',
            'clientName': 'Retail Chain Ltd',
            'memberFirm': 'AU',
            'country': 'Australia',
            'globalBusiness': 'Tax & Legal',
            'globalBusinessServiceArea': 'Tax',
            'globalBusinessServiceLine': 'Tax Advisory',
            'globalIndustry': 'Consumer Products',
            'globalIndustrySector': 'Retail',
            'engagementCode': 'ENG-100004',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '2024-01-01',
            'engagementEndDate': '2024-05-31',
            'dueDate': '2024-04-15',  # Earliest due date
            'status': 'Not Started',
            'needLLMDateValidation': False,
        },
        100005: {
            'activityId': 100005,
            'activityName': 'Strategy Consulting - No Due Date',
            'activityType': 'Contribute qual',
            'clientName': 'European Consulting Group',
            'memberFirm': 'DE',
            'country': 'Germany',
            'globalBusiness': 'Consulting',
            'globalBusinessServiceArea': 'Strategy & Operations',
            'globalBusinessServiceLine': 'Strategy',
            'globalIndustry': 'Energy & Resources',
            'globalIndustrySector': 'Oil & Gas',
            'engagementCode': 'ENG-100005',
            'globalLCSPEmails': ['<EMAIL>'],
            'engagementLepEmails': ['<EMAIL>'],
            'engagementManagerEmails': ['<EMAIL>'],
            'activityOwnerEmails': ['<EMAIL>'],
            'engagementStartDate': '2024-06-01',
            'engagementEndDate': '2024-12-31',
            'dueDate': None,  # No due date - should be sorted last
            'status': 'Not Started',
            'needLLMDateValidation': False,
        },
    }

    MOCKED_LIST_TASKS: Dict[str, Any] = {
        'data': [
            {
                'id': {'value': 100001},
                'activityName': {'value': 'Quality Review - Late Due Date'},
                'type': {'value': 'Contribute engagement'},
                'clientNameLocal': {'value': 'TechCorp Industries'},
                'memberFirm': {'value': 'US'},
                'country': {'value': 'United States'},
                'globalBusiness': {'value': 'Audit & Assurance'},
                'globalBusinessServiceArea': {'value': 'Audit'},
                'globalBusinessServiceLine': {'value': 'External Audit'},
                'globalIndustry': {'value': 'Technology, Media & Telecommunications'},
                'globalIndustrySector': {'value': 'Software & Computer Services'},
                'globalLCSPEmails': {'value': ['<EMAIL>']},
                'owner': {'value': [{'email': '<EMAIL>'}]},
                'engagement': {
                    'lep': {'value': [{'email': '<EMAIL>'}]},
                    'manager': {'value': [{'email': '<EMAIL>'}]},
                    'startDate': {'value': '01/01/2024'},  # Ambiguous format
                    'endDate': {'value': '31/12/2024'},  # Ambiguous format
                    'code': {'value': 'ENG-100001'},
                },
                'dueDate': {'value': '2024-12-30T00:00:00Z'},
                'status': {'value': 'Not Started'},
            },
            {
                'id': {'value': 100002},
                'activityName': {'value': 'Risk Assessment - Middle Due Date'},
                'type': {'value': 'Contribute qual'},
                'clientNameLocal': {'value': 'Global Financial Solutions Ltd'},
                'memberFirm': {'value': 'UK'},
                'country': {'value': 'United Kingdom'},
                'globalBusiness': {'value': 'Risk Advisory'},
                'globalBusinessServiceArea': {'value': 'Risk & Financial Advisory'},
                'globalBusinessServiceLine': {'value': 'Regulatory & Legal Support'},
                'globalIndustry': {'value': 'Financial Services'},
                'globalIndustrySector': {'value': 'Investment Banking'},
                'globalLCSPEmails': {'value': ['<EMAIL>']},
                'owner': {'value': [{'email': '<EMAIL>'}]},
                'engagement': {
                    'lep': {'value': [{'email': '<EMAIL>'}]},
                    'manager': {'value': [{'email': '<EMAIL>'}]},
                    'startDate': {'value': 'March 10'},  # Missing year
                    'endDate': {'value': 'September 30, 2024'},  # Natural language
                    'code': {'value': 'ENG-100002'},
                },
                'dueDate': {'value': '2024-06-15T00:00:00Z'},
                'status': {'value': 'In Progress'},
            },
            {
                'id': {'value': 100003},
                'activityName': {'value': 'Compliance Review - Should be Filtered'},
                'type': {'value': 'Contribute qual'},
                'clientNameLocal': {'value': 'Manufacturing Corp'},
                'memberFirm': {'value': 'CA'},
                'country': {'value': 'Canada'},
                'globalBusiness': {'value': 'Audit & Assurance'},
                'globalBusinessServiceArea': {'value': 'Audit'},
                'globalBusinessServiceLine': {'value': 'Internal Audit'},
                'globalIndustry': {'value': 'Manufacturing'},
                'globalIndustrySector': {'value': 'Industrial Manufacturing'},
                'globalLCSPEmails': {'value': ['<EMAIL>']},
                'owner': {'value': [{'email': '<EMAIL>'}]},
                'engagement': {
                    'lep': {'value': [{'email': '<EMAIL>'}]},
                    'manager': {'value': [{'email': '<EMAIL>'}]},
                    'startDate': {'value': 'Februart 1, 2024'},  # Typo in month
                    'endDate': {'value': 'November 30, 2024'},  # Good format
                    'code': {'value': 'ENG-100003'},
                },
                'dueDate': {'value': '2024-05-01T00:00:00Z'},
                'status': {'value': 'Not Started'},
            },
            {
                'id': {'value': 100004},
                'activityName': {'value': 'Tax Advisory - Earliest Due Date'},
                'type': {'value': 'Contribute qual'},
                'clientNameLocal': {'value': 'Retail Chain Ltd'},
                'memberFirm': {'value': 'AU'},
                'country': {'value': 'Australia'},
                'globalBusiness': {'value': 'Tax & Legal'},
                'globalBusinessServiceArea': {'value': 'Tax'},
                'globalBusinessServiceLine': {'value': 'Tax Advisory'},
                'globalIndustry': {'value': 'Consumer Products'},
                'globalIndustrySector': {'value': 'Retail'},
                'globalLCSPEmails': {'value': ['<EMAIL>']},
                'owner': {'value': [{'email': '<EMAIL>'}]},
                'engagement': {
                    'lep': {'value': [{'email': '<EMAIL>'}]},
                    'manager': {'value': [{'email': '<EMAIL>'}]},
                    'startDate': {'value': '2024-01-01T00:00:00Z'},  # Good ISO format
                    'endDate': {'value': '2024-05-31T00:00:00Z'},  # Good ISO format
                    'code': {'value': 'ENG-100004'},
                },
                'dueDate': {'value': '2024-04-15T00:00:00Z'},
                'status': {'value': 'Not Started'},
            },
            {
                'id': {'value': 100005},
                'activityName': {'value': 'Strategy Consulting - No Due Date'},
                'type': {'value': 'Contribute qual'},
                'clientNameLocal': {'value': 'European Consulting Group'},
                'memberFirm': {'value': 'DE'},
                'country': {'value': 'Germany'},
                'globalBusiness': {'value': 'Consulting'},
                'globalBusinessServiceArea': {'value': 'Strategy & Operations'},
                'globalBusinessServiceLine': {'value': 'Strategy'},
                'globalIndustry': {'value': 'Energy & Resources'},
                'globalIndustrySector': {'value': 'Oil & Gas'},
                'globalLCSPEmails': {'value': ['<EMAIL>']},
                'owner': {'value': [{'email': '<EMAIL>'}]},
                'engagement': {
                    'lep': {'value': [{'email': '<EMAIL>'}]},
                    'manager': {'value': [{'email': '<EMAIL>'}]},
                    'startDate': {'value': '2024-06-01T00:00:00Z'},  # Good ISO format
                    'endDate': {'value': '2024-12-31T00:00:00Z'},  # Good ISO format
                    'code': {'value': 'ENG-100005'},
                },
                'dueDate': {'value': None},  # No due date
                'status': {'value': 'Not Started'},
            },
        ]
    }

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the KX Dash Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
            date_validator_service: Optional DateValidatorService for LLM date validation
        """
        self._http_client = http_client
        self._base_path = settings.kx_dash_api.base_url

    async def list(self, token: str) -> List[Dict[str, Any]]:
        """
        Get list of activities from KX Quals API.

        Returns:
            list[dict[str, Any]]: List of activities in dash format
        """
        if settings.environment == Environment.LOCAL:
            return self._parse_kx_dash_list_response(self.MOCKED_LIST_TASKS)

        url = url_join(self._base_path, 'getMyFilteredTasks')
        headers = {'Authorization': f'Bearer {token}'}
        response = (await self._http_client.get(url, headers=headers)).json()
        return self._parse_kx_dash_list_response(response)

    async def get(self, activity_id: int, token: str) -> Dict[str, Any] | None:
        """
        Get a specific activity from KX Quals API.

        Args:
            activity_id: The ID of the activity to retrieve

        Returns:
            dict[str, Any] | None: Activity data in dash format or None if not found
        """
        if settings.environment == Environment.LOCAL:
            return self._parse_kx_dash_get_response(self.MOCKED_TASKS.get(activity_id))

        url = url_join(self._base_path, 'getActivityDataById')
        headers = {'Authorization': f'Bearer {token}'}
        response = (await self._http_client.get(url, params={'activityId': activity_id}, headers=headers)).json()

        return self._parse_kx_dash_get_response(response)

    @staticmethod
    def _parse_date_string(date_string: str | None) -> str | None:
        """
        Parse date string from API response and convert to date format.

        Args:
            date_string: Date string in format '2025-04-19T00:00:00Z' or '2025-04-19'

        Returns:
            Date string in format '2025-04-19' or None
        """
        if not date_string:
            return None

        # Parse datetime string and extract date part
        try:
            parsed_datetime = parse(date_string)
            return parsed_datetime.date().isoformat()
        except Exception:
            return None

    def _parse_kx_dash_get_response(self, response: Dict[str, Any] | None) -> Dict[str, Any] | None:
        """
        Parse KX Quals API response and convert it to dash task format.
        """
        if not response:
            return None

        # Parse engagement dates
        start_date = self._parse_date_string(response.get('engagementStartDate'))
        end_date = self._parse_date_string(response.get('engagementEndDate'))
        response['engagementStartDate'] = start_date if start_date else response.get('engagementStartDate')
        response['engagementEndDate'] = end_date if end_date else response.get('engagementEndDate')

        response[self._NEED_LLM_DATE_VALIDATION_KEY] = True if not start_date or not end_date else False
        return response

    def _parse_kx_dash_list_response(self, response: Dict[str, Any] | None) -> List[Dict[str, Any]]:
        """
        Parse KX Quals API response and convert it to dash task format.

        Args:
            response: Raw response from KX Quals API

        Returns:
            list[dict[str, Any]]: List of tasks in dash format
        """
        if not response:
            return []

        tasks = []
        for item in response.get('data', []):
            # Extract owner information
            owners = item.get('owner', {}).get('value', [])
            owner_emails = [owner.get('email') for owner in owners if owner.get('email')]

            # Extract engagement information
            engagement = item.get('engagement', {})
            engagement_lep = engagement.get('lep', {}).get('value', [])
            lep_emails = [lep.get('email') for lep in engagement_lep if lep.get('email')]
            engagement_manager = engagement.get('manager', {}).get('value', [])
            manager_emails = [manager.get('email') for manager in engagement_manager if manager.get('email')]

            # Parse engagement dates
            engagement_start_date = engagement.get('startDate', {}).get('value')
            engagement_end_date = engagement.get('endDate', {}).get('value')
            parsed_engagement_start_date = self._parse_date_string(engagement_start_date)
            parsed_engagement_end_date = self._parse_date_string(engagement_end_date)

            # Parse due date
            due_date = self._parse_date_string(item.get('dueDate', {}).get('value'))

            task = {
                'activityId': item.get('id', {}).get('value'),
                'activityName': item.get('activityName', {}).get('value'),
                'activityType': item.get('type', {}).get('value'),
                'clientName': item.get('clientNameLocal', {}).get('value'),
                'memberFirm': item.get('memberFirm', {}).get('value'),
                'country': item.get('country', {}).get('value'),
                'globalBusiness': item.get('globalBusiness', {}).get('value'),
                'globalBusinessServiceArea': item.get('globalBusinessServiceArea', {}).get('value'),
                'globalBusinessServiceLine': item.get('globalBusinessServiceLine', {}).get('value'),
                'globalIndustry': item.get('globalIndustry', {}).get('value'),
                'globalIndustrySector': item.get('globalIndustrySector', {}).get('value'),
                'engagementCode': engagement.get('code', {}).get('value'),
                'globalLCSPEmails': item.get('globalLCSPEmails', {}).get('value', []),
                'engagementLepEmails': lep_emails,
                'engagementManagerEmails': manager_emails,
                'activityOwnerEmails': owner_emails,
                'engagementStartDate': parsed_engagement_start_date
                if parsed_engagement_start_date
                else engagement_start_date,
                'engagementEndDate': parsed_engagement_end_date if parsed_engagement_end_date else engagement_end_date,
                'dueDate': due_date,
                'status': item.get('status', {}).get('value'),
            }
            task[self._NEED_LLM_DATE_VALIDATION_KEY] = (
                True if not parsed_engagement_start_date or not parsed_engagement_end_date else False
            )
            tasks.append(task)

        return tasks
