from datetime import datetime
from unittest.mock import AsyncMock
from uuid import UUID

import pytest

from constants.message import ConversationMessageIntention, MessageRole, MessageType, SystemReplyType
from schemas.conversation_message.message import UserMessageSerializer
from services.suggestions import SuggestedPromptsGenerator, SuggestedUserPrompt


class TestSuggestedPromptsGenerator:
    @pytest.mark.asyncio
    async def test_dash_discard_message_intention(self):
        suggested_prompts_generator = SuggestedPromptsGenerator(
            conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
            user_message=UserMessageSerializer(
                id=UUID('00000000-0000-0000-0000-000000000000'),
                conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
                role=MessageRole.USER,
                type=MessageType.TEXT,
                content='test',
                created_at=datetime.now(),
                selected_option=None,
            ),
            intention=ConversationMessageIntention.DASH_DISCARD,
            conversation_message_history=[],
            confirmed_data=AsyncMock(),
            aggregated_data=AsyncMock(),
            current_reply_type=SystemReplyType.EMPTY,
            called_from=self.test_dash_discard_message_intention.__name__,
        )

        suggested_prompts = await suggested_prompts_generator.run()

        assert suggested_prompts == [SuggestedUserPrompt.UPLOAD_DOCUMENT, SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION]
