"""Tests for ComprehensiveExtractionService."""

import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

from schemas import AggregatedData
from services.comprehensive_extraction_service import ComprehensiveExtractionService


class TestComprehensiveExtractionService:
    """Test cases for ComprehensiveExtractionService."""

    @pytest.fixture
    def mock_conversation_message_repository(self):
        """Mock conversation message repository."""
        return AsyncMock()

    @pytest.fixture
    def mock_document_service(self):
        """Mock document service."""
        mock_service = AsyncMock()
        mock_service.storage_account_name = "teststorage"
        return mock_service

    @pytest.fixture
    def mock_extracted_data_service(self):
        """Mock extracted data service."""
        return AsyncMock()

    @pytest.fixture
    def comprehensive_extraction_service(
        self,
        mock_conversation_message_repository,
        mock_document_service,
        mock_extracted_data_service,
    ):
        """Create ComprehensiveExtractionService instance with mocked dependencies."""
        return ComprehensiveExtractionService(
            conversation_message_repository=mock_conversation_message_repository,
            document_service=mock_document_service,
            extracted_data_service=mock_extracted_data_service,
        )

    @pytest.fixture
    def test_conversation_id(self):
        """Test conversation ID."""
        return uuid4()

    @pytest.fixture
    def sample_aggregated_data(self):
        """Sample aggregated data for testing."""
        return AggregatedData(
            client_name=["Test Client"],
            ldmf_country=["United States"],
            date_intervals=[("2024-01-01", "2024-06-30")],
            objective_and_scope="Digital transformation",
            outcomes="Improved efficiency",
            business_issues="Legacy system challenges",
            scope_approach="Agile methodology",
            value_delivered="40% productivity increase",
            engagement_summary="Comprehensive digital transformation project",
            one_line_description="Digital transformation for Test Client",
            client_references="Excellent partnership",
            client_name_sharing="Approved for case studies",
            client_industry=["Technology", "Healthcare"],
            engagement_dates=["Q1-Q2 2024"],
            engagement_locations=["New York", "Remote"],
            engagement_fee_display="$1M",
            client_services=["Consulting", "Implementation"],
            source_of_work="RFP response",
            qual_usage="Business development",
            team_roles="Lead Partner, Manager, Analysts",
            approver="John Smith, Partner",
        )

    async def test_trigger_comprehensive_extraction_success(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        mock_conversation_message_repository,
        sample_aggregated_data,
        mock_extracted_data_service,
    ):
        """Test successful comprehensive extraction trigger."""
        # Mock conversation messages with documents
        mock_message = MagicMock()
        mock_message.documents = [MagicMock(file_name="test_document.pdf")]
        mock_message.public_id = uuid4()
        
        mock_conversation_message_repository.get_combined_history.return_value = [mock_message]
        mock_extracted_data_service.aggregate_data.return_value = sample_aggregated_data

        # Execute the method
        await comprehensive_extraction_service.trigger_comprehensive_extraction(test_conversation_id)

        # Verify calls were made
        mock_conversation_message_repository.get_combined_history.assert_called_once_with(test_conversation_id)
        mock_extracted_data_service.aggregate_data.assert_called_once_with(test_conversation_id)

    async def test_trigger_comprehensive_extraction_no_chunks(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        mock_conversation_message_repository,
    ):
        """Test comprehensive extraction when no chunks are found."""
        # Mock empty conversation messages
        mock_conversation_message_repository.get_combined_history.return_value = []

        # Execute the method
        await comprehensive_extraction_service.trigger_comprehensive_extraction(test_conversation_id)

        # Verify get_combined_history was called
        mock_conversation_message_repository.get_combined_history.assert_called_once_with(test_conversation_id)

    async def test_collect_existing_chunks_with_documents(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        mock_conversation_message_repository,
    ):
        """Test collecting existing chunks when documents are present."""
        # Mock conversation messages with documents
        mock_message = MagicMock()
        mock_document = MagicMock()
        mock_document.file_name = "test_document.pdf"
        mock_message.documents = [mock_document]
        mock_message.public_id = uuid4()
        
        mock_conversation_message_repository.get_combined_history.return_value = [mock_message]

        # Execute the method
        chunk_urls = await comprehensive_extraction_service.collect_existing_chunks(test_conversation_id)

        # Verify chunk URLs were generated
        assert len(chunk_urls) > 0
        assert all("test_document.pdf_chunk_" in url for url in chunk_urls)
        assert all("teststorage.blob.core.windows.net" in url for url in chunk_urls)

    async def test_collect_existing_chunks_no_documents(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        mock_conversation_message_repository,
    ):
        """Test collecting existing chunks when no documents are present."""
        # Mock conversation messages without documents
        mock_message = MagicMock()
        mock_message.documents = []
        
        mock_conversation_message_repository.get_combined_history.return_value = [mock_message]

        # Execute the method
        chunk_urls = await comprehensive_extraction_service.collect_existing_chunks(test_conversation_id)

        # Verify no chunk URLs were generated
        assert len(chunk_urls) == 0

    async def test_collect_existing_chunks_exception_handling(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        mock_conversation_message_repository,
    ):
        """Test exception handling in collect_existing_chunks."""
        # Mock repository to raise exception
        mock_conversation_message_repository.get_combined_history.side_effect = Exception("Database error")

        # Execute the method
        chunk_urls = await comprehensive_extraction_service.collect_existing_chunks(test_conversation_id)

        # Verify empty list is returned on exception
        assert chunk_urls == []

    async def test_simulate_enhanced_extraction(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        sample_aggregated_data,
        mock_extracted_data_service,
    ):
        """Test simulated enhanced extraction."""
        # Mock aggregated data
        mock_extracted_data_service.aggregate_data.return_value = sample_aggregated_data

        # Execute the method
        await comprehensive_extraction_service.simulate_enhanced_extraction(test_conversation_id)

        # Verify aggregate_data was called
        mock_extracted_data_service.aggregate_data.assert_called_once_with(test_conversation_id)

    async def test_get_extraction_status_success(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        sample_aggregated_data,
        mock_extracted_data_service,
    ):
        """Test getting extraction status successfully."""
        # Mock aggregated data
        mock_extracted_data_service.aggregate_data.return_value = sample_aggregated_data

        # Execute the method
        status = await comprehensive_extraction_service.get_extraction_status(test_conversation_id)

        # Verify status structure
        assert status['conversation_id'] == str(test_conversation_id)
        assert status['core_fields_populated'] is True
        assert status['comprehensive_fields_populated'] is True
        assert status['client_names_count'] == 1
        assert status['countries_count'] == 1
        assert status['date_intervals_count'] == 1
        assert status['has_business_issues'] is True
        assert status['has_scope_approach'] is True
        assert status['has_value_delivered'] is True
        assert status['has_engagement_summary'] is True
        assert status['has_client_references'] is True
        assert status['has_team_roles'] is True

    async def test_get_extraction_status_exception_handling(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        mock_extracted_data_service,
    ):
        """Test exception handling in get_extraction_status."""
        # Mock service to raise exception
        mock_extracted_data_service.aggregate_data.side_effect = Exception("Service error")

        # Execute the method
        status = await comprehensive_extraction_service.get_extraction_status(test_conversation_id)

        # Verify error status
        assert status['conversation_id'] == str(test_conversation_id)
        assert status['status'] == 'error'
        assert 'error' in status

    async def test_trigger_enhanced_extraction_workflow(
        self,
        comprehensive_extraction_service,
        test_conversation_id,
        sample_aggregated_data,
        mock_extracted_data_service,
    ):
        """Test triggering enhanced extraction workflow."""
        # Mock aggregated data
        mock_extracted_data_service.aggregate_data.return_value = sample_aggregated_data
        
        chunk_urls = [
            "https://teststorage.blob.core.windows.net/chat-attachments/chunks/doc1_chunk_0.json",
            "https://teststorage.blob.core.windows.net/chat-attachments/chunks/doc1_chunk_1.json",
        ]

        # Execute the method
        await comprehensive_extraction_service.trigger_enhanced_extraction_workflow(
            test_conversation_id, chunk_urls
        )

        # Verify simulate_enhanced_extraction was called
        mock_extracted_data_service.aggregate_data.assert_called_once_with(test_conversation_id)
