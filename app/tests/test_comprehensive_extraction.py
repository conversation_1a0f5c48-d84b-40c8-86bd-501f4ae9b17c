"""Tests for comprehensive document extraction and data processing system."""

import pytest
from uuid import uuid4

from constants.extracted_data import DataSourceType
from schemas import AggregatedData, ExtractedData
from schemas.confirmed_data import ConfirmedData
from services.extracted_data.strategies import (
    ConcatenationStrategy,
    EnhancedMergeUniqueValuesStrategy,
    EnhancedPriorityOverrideStrategy,
    EnhancedDateIntervalCollectionStrategy,
)


class TestComprehensiveFieldMerging:
    """Test comprehensive field merging strategies."""

    def test_concatenation_strategy(self):
        """Test concatenation strategy for descriptive fields."""
        strategy = ConcatenationStrategy()
        
        # Create test extracted data with new fields
        extracted_data1 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.DOCUMENTS,
            created_at="2025-01-16T12:00:00Z",
            business_issues="Cost reduction challenges",
            scope_approach="Lean methodology implementation",
            value_delivered="30% cost savings",
        )
        
        extracted_data2 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.PROMPT,
            created_at="2025-01-16T12:00:00Z",
            business_issues="Process inefficiencies",
            scope_approach="Digital transformation",
            value_delivered="Improved operational efficiency",
        )
        
        # Process both data sources
        strategy.process(extracted_data1)
        strategy.process(extracted_data2)
        
        # Verify concatenation results
        assert strategy.get_business_issues() == "Cost reduction challenges | Process inefficiencies"
        assert strategy.get_scope_approach() == "Lean methodology implementation | Digital transformation"
        assert strategy.get_value_delivered() == "30% cost savings | Improved operational efficiency"

    def test_enhanced_merge_unique_strategy(self):
        """Test enhanced merge unique strategy for list fields."""
        strategy = EnhancedMergeUniqueValuesStrategy()
        
        # Create test extracted data with list fields
        extracted_data1 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.DOCUMENTS,
            created_at="2025-01-16T12:00:00Z",
            client_industry="Technology, Healthcare",
            engagement_locations="New York, London",
            client_services="Consulting, Implementation",
        )
        
        extracted_data2 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.PROMPT,
            created_at="2025-01-16T12:00:00Z",
            client_industry="Technology, Finance",
            engagement_locations="London, Tokyo",
            client_services="Consulting, Training",
        )
        
        # Process both data sources
        strategy.process(extracted_data1)
        strategy.process(extracted_data2)
        
        # Verify unique merging results
        client_industry = strategy.get_client_industry()
        assert "Technology" in client_industry
        assert "Healthcare" in client_industry
        assert "Finance" in client_industry
        assert len(client_industry) == 3  # Unique values only
        
        engagement_locations = strategy.get_engagement_locations()
        assert "New York" in engagement_locations
        assert "London" in engagement_locations
        assert "Tokyo" in engagement_locations
        assert len(engagement_locations) == 3  # Unique values only

    def test_enhanced_priority_override_strategy(self):
        """Test enhanced priority override strategy."""
        strategy = EnhancedPriorityOverrideStrategy()
        
        # Create test extracted data with override fields
        extracted_data1 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.DOCUMENTS,
            created_at="2025-01-16T12:00:00Z",
            engagement_fee_display="$500K",
        )
        
        extracted_data2 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.PROMPT,
            created_at="2025-01-16T12:00:00Z",
            engagement_fee_display="$750K",
        )
        
        # Process both data sources (second should override first)
        strategy.process(extracted_data1)
        strategy.process(extracted_data2)
        
        # Verify override behavior (latest value wins)
        assert strategy.get_engagement_fee_display() == "$750K"

    def test_enhanced_date_interval_strategy(self):
        """Test enhanced date interval strategy."""
        strategy = EnhancedDateIntervalCollectionStrategy()
        
        # Create test extracted data with date fields
        extracted_data1 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.DOCUMENTS,
            created_at="2025-01-16T12:00:00Z",
            engagement_dates="Q1 2024 - Q2 2024",
        )
        
        extracted_data2 = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.PROMPT,
            created_at="2025-01-16T12:00:00Z",
            engagement_dates="January 2024 - June 2024",
        )
        
        # Process both data sources
        strategy.process(extracted_data1)
        strategy.process(extracted_data2)
        
        # Verify date collection
        engagement_dates = strategy.get_engagement_dates()
        assert "Q1 2024 - Q2 2024" in engagement_dates
        assert "January 2024 - June 2024" in engagement_dates
        assert len(engagement_dates) == 2


class TestComprehensiveSchemas:
    """Test comprehensive schema extensions."""

    def test_aggregated_data_comprehensive_fields(self):
        """Test AggregatedData with comprehensive fields."""
        aggregated_data = AggregatedData(
            # Core fields
            client_name=["Test Client"],
            ldmf_country=["United States"],
            date_intervals=[("2024-01-01", "2024-06-30")],
            objective_and_scope="Digital transformation",
            outcomes="Improved efficiency",
            
            # New comprehensive fields
            business_issues="Legacy system challenges",
            scope_approach="Agile methodology",
            value_delivered="40% productivity increase",
            engagement_summary="Comprehensive digital transformation project",
            one_line_description="Digital transformation for Test Client",
            client_references="Excellent partnership",
            client_name_sharing="Approved for case studies",
            client_industry=["Technology", "Healthcare"],
            engagement_dates=["Q1-Q2 2024"],
            engagement_locations=["New York", "Remote"],
            engagement_fee_display="$1M",
            client_services=["Consulting", "Implementation"],
            source_of_work="RFP response",
            qual_usage="Business development",
            team_roles="Lead Partner, Manager, Analysts",
            approver="John Smith, Partner",
        )
        
        # Test core functionality still works
        assert not aggregated_data.is_empty
        assert aggregated_data.comprehensive_fields_populated
        
        # Test new fields are accessible
        assert aggregated_data.business_issues == "Legacy system challenges"
        assert aggregated_data.client_industry == ["Technology", "Healthcare"]
        assert aggregated_data.engagement_fee_display == "$1M"

    def test_confirmed_data_comprehensive_validation(self):
        """Test ConfirmedData comprehensive field validation."""
        confirmed_data = ConfirmedData(
            # Core fields
            client_name="Test Client",
            ldmf_country="United States",
            date_intervals=("2024-01-01", "2024-06-30"),
            objective_and_scope="Digital transformation",
            outcomes="Improved efficiency",
            
            # New comprehensive fields
            business_issues="Legacy system challenges",
            scope_approach="Agile methodology",
            value_delivered="40% productivity increase",
            engagement_summary="Comprehensive digital transformation project",
            one_line_description="Digital transformation for Test Client",
            client_references="Excellent partnership",
            client_name_sharing="Approved for case studies",
            client_industry="Technology",
            engagement_dates="Q1-Q2 2024",
            engagement_locations="New York",
            engagement_fee_display="$1M",
            client_services="Consulting",
            source_of_work="RFP response",
            qual_usage="Business development",
            team_roles="Lead Partner, Manager",
            approver="John Smith, Partner",
        )
        
        # Test core validation still works
        assert confirmed_data.required_fields_are_complete
        
        # Test comprehensive validation
        assert confirmed_data.comprehensive_fields_are_complete

    def test_confirmed_data_partial_completion(self):
        """Test ConfirmedData with only core fields completed."""
        confirmed_data = ConfirmedData(
            # Only core fields
            client_name="Test Client",
            ldmf_country="United States",
            date_intervals=("2024-01-01", "2024-06-30"),
            objective_and_scope="Digital transformation",
            outcomes="Improved efficiency",
        )
        
        # Core validation should pass
        assert confirmed_data.required_fields_are_complete
        
        # Comprehensive validation should fail
        assert not confirmed_data.comprehensive_fields_are_complete


class TestExtractedDataModel:
    """Test ExtractedData model with new fields."""

    def test_extracted_data_with_comprehensive_fields(self):
        """Test ExtractedData model with all comprehensive fields."""
        extracted_data = ExtractedData(
            conversation_id=uuid4(),
            data_source_type=DataSourceType.DOCUMENTS,
            created_at="2025-01-16T12:00:00Z",
            
            # Core fields
            client_name=["Test Client"],
            ldmf_country=["United States"],
            title="Digital Transformation Project",
            objective_and_scope="Modernize legacy systems",
            outcomes="Improved operational efficiency",
            
            # New comprehensive fields
            business_issues="Outdated technology stack",
            scope_approach="Phased implementation approach",
            value_delivered="50% reduction in processing time",
            engagement_summary="End-to-end digital transformation",
            one_line_description="Legacy system modernization",
            client_references="Outstanding results achieved",
            client_name_sharing="Permission granted for testimonials",
            client_industry="Financial Services",
            engagement_dates="January 2024 - December 2024",
            engagement_locations="New York, London",
            engagement_fee_display="$2.5M",
            client_services="Strategy, Implementation, Training",
            source_of_work="Existing client relationship",
            qual_usage="Proposal support",
            team_roles="Partner, Director, Senior Manager, Consultants",
            approver="Jane Doe, Managing Director",
        )
        
        # Verify all fields are accessible
        assert extracted_data.business_issues == "Outdated technology stack"
        assert extracted_data.scope_approach == "Phased implementation approach"
        assert extracted_data.value_delivered == "50% reduction in processing time"
        assert extracted_data.engagement_summary == "End-to-end digital transformation"
        assert extracted_data.one_line_description == "Legacy system modernization"
        assert extracted_data.client_references == "Outstanding results achieved"
        assert extracted_data.client_name_sharing == "Permission granted for testimonials"
        assert extracted_data.client_industry == "Financial Services"
        assert extracted_data.engagement_dates == "January 2024 - December 2024"
        assert extracted_data.engagement_locations == "New York, London"
        assert extracted_data.engagement_fee_display == "$2.5M"
        assert extracted_data.client_services == "Strategy, Implementation, Training"
        assert extracted_data.source_of_work == "Existing client relationship"
        assert extracted_data.qual_usage == "Proposal support"
        assert extracted_data.team_roles == "Partner, Director, Senior Manager, Consultants"
        assert extracted_data.approver == "Jane Doe, Managing Director"
        
        # Test model_dump_for_db functionality
        db_data = extracted_data.model_dump_for_db()
        assert 'business_issues' in db_data
        assert 'scope_approach' in db_data
        assert 'value_delivered' in db_data
