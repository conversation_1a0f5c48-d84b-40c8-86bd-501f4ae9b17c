from uuid import UUID

from fastapi import status
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from repositories import ConversationMessageRepository, ConversationRepository

from .extracted_data_service import AutoCommitRepository


__all__ = [
    'conversation_data',
    'test_conversation_id',
    'conversation_repository_dep',
    'conversation_repository_dep_with_autocommit',
    'conversation_message_repository_dep',
    'conversation_message_repository_dep_with_autocommit',
]


@pytest.fixture(scope='session')
def conversation_data():
    """Fixture that provides standard test conversation data."""
    return {
        'dash_activity_id': None,
    }


@pytest.fixture
async def test_conversation_id(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Create a test conversation and return its ID."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    assert response.status_code == status.HTTP_201_CREATED, response.json()
    return UUID(response.json()['conversation']['id'])


@pytest.fixture
async def conversation_repository_dep(db_session):
    return ConversationRepository(db_session=db_session)


@pytest.fixture
async def conversation_repository_dep_with_autocommit(db_engine):
    """Fixture providing ConversationRepository with its own session."""

    async with AsyncSession(db_engine) as session:
        conversation_repository = ConversationRepository(db_session=session)
        async with session.begin():
            yield AutoCommitRepository(conversation_repository, session)


@pytest.fixture
async def conversation_message_repository_dep(
    db_session,
    conversation_repository_dep,
):
    return ConversationMessageRepository(
        db_session=db_session,
        conversation_repository=conversation_repository_dep,
    )


@pytest.fixture
async def conversation_message_repository_dep_with_autocommit(
    db_engine,
    conversation_repository_dep_with_autocommit,
):
    """Fixture providing ConversationMessageRepository with its own session."""

    async with AsyncSession(db_engine) as session:
        conversation_message_repository = ConversationMessageRepository(
            db_session=session,
            conversation_repository=conversation_repository_dep_with_autocommit,
        )
        async with session.begin():
            yield AutoCommitRepository(conversation_message_repository, session)
